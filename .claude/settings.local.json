{"permissions": {"allow": ["mcp__ide__getDiagnostics", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run test:*)", "Bash(npm run lint:*)", "Bash(git restore:*)", "Bash(npm run start:*)", "Bash(npm run build:*)", "Bash(grep:*)", "Bash(rg:*)", "Bash(find:*)", "Bash(git add:*)", "Bash(git push:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(chmod:*)", "Bash(node:*)", "Bash(ng build:*)", "Bash(npm test:*)", "Bash(npx eslint:*)", "Bash(ls:*)", "Bash(npx tsc:*)", "Bash(for file in docs/stories/features-critical-enhancement/MCFC-{004,005,007,015,018}-*.md)", "Bash(do)", "Bash(if [ -f \"$file\" ])", "<PERSON><PERSON>(then)", "Bash(echo \"Updating $file\")", "<PERSON><PERSON>(sed:*)", "Bash(fi)", "Bash(done)", "Bash(npx cap sync:*)", "Bash(xcodebuild:*)", "<PERSON><PERSON>(sudo launchctl:*)", "Bash(launchctl limit:*)", "Bash(xcrun simctl shutdown:*)", "Bash(xcrun simctl erase:*)", "Bash(rm:*)", "Bash(pod update:*)", "Bash(npm install:*)", "Bash(pod install:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run:*)", "Bash(npx ng lint:*)", "Bash(git log:*)", "Bash(git commit:*)", "Bash(ionic serve:*)", "Bash(ng serve:*)", "<PERSON><PERSON>(echo:*)"], "deny": []}}