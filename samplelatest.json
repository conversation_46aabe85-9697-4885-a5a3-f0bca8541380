{"googleApiKey": "AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0", "apiId": "812275411", "appCode": "start", "appName": "Loyalty Client Mobile App", "appVersion": "0.0.1", "useAuth": true, "useION": true, "useISO": false, "defaultNotAuthURL": "", "autoLogout": true, "autoLogoutTimeout": 180, "autoLogoutWarning": 120, "defaultLat": -28.83688693522886, "defaultLng": 25.**************, "loadIdentity": false, "identityBaseUrl": "http://payroll.dv.lss.si/servlet/systemImage", "appBaseUrl": "http://alpine", "apiBaseUrl": "https://ffz1qa.loyaltyplus.aero/", "logEndpoint": "https://ffz1qa.loyaltyplus.aero/extsecure/tools/logservice", "configAPIUrl": "http://ffz1qa.loyaltyplus.aero/config/api/v1/", "memberPhone": {"dialCode": "+27", "nationalNumber": ""}, "memberCard": "", "telephone": {"selectFirstCountry": true, "preferredCountries": ["za"], "onlyCountries": ["za", "ls", "bw", "na", "sz", "mz"]}, "pages": [{"title": "home", "path": "home", "secure": true, "class": "bg-red-500 h-full", "components": [{"type": "PagesLandingTheme1Component", "showWhen": "authenticated", "inputs": {"padding": true, "className": "bg-black rounded shadow", "title": "Welcome", "childrenConfig": [{"type": "WelcomeComponent", "inputs": {"name": "<PERSON>", "surname": "<PERSON><PERSON>"}}]}}, {"type": "PagesLoginTheme1Component", "showWhen": "anonymous", "inputs": {"kc": "kc", "memberService": "memberService", "router": "router", "title": {"text": "Welcome", "class": "text-primaryC<PERSON>rast text-4xl text-center"}, "subtitle": {"text": "to Loyalty Plus Demo", "class": "text-primaryC<PERSON><PERSON>t text-center w-full"}, "icon": {"src": "assets/images/logo.png", "class": "rounded-large text-center w-80 mx-auto"}, "auth_buttons": {"class": " px-4 mt-6", "login": {"text": "Sign in", "class": "primary text-sm text-center w-full p-5 rounded-lg shadow"}, "signup": {"text": "Sign up", "class": "primary text-sm text-center w-full p-5 rounded-lg shadow my-3"}, "password": {"text": "Forgot Password", "class": "primary text-sm text-center w-full p-5 rounded-lg shadow"}}, "social_buttons": {"class": "flex w-full text-center mt-6", "facebook": {"icon": "logo-facebook", "class": "bg-clear text-primaryC<PERSON>rast rounded-full p-1 text-2xl", "size": "large"}, "twitter": {"icon": "logo-x", "size": "large", "class": "bg-clear text-primaryC<PERSON>rast rounded-full p-1 text-2xl"}, "linkedin": {"icon": "logo-linkedin", "size": "large", "class": "bg-clear text-primaryC<PERSON>rast rounded-full p-1 text-2xl"}, "youtube": {"icon": "logo-youtube", "size": "large", "class": "bg-clear text-primaryC<PERSON>rast rounded-full p-1 text-2xl"}, "pinterest": {"icon": "logo-pinterest", "size": "large", "class": "bg-clear text-primaryC<PERSON>rast rounded-full p-1 text-2xl"}, "instagram": {"icon": "logo-instagram", "size": "large", "class": "bg-clear text-primaryC<PERSON>rast rounded-full p-1 text-2xl"}}, "loggedinIcon": "assets/images/logo.png"}}]}, {"title": "profile", "path": "profile", "secure": true, "class": "bg-blue-500 h-full", "components": [{"type": "ProfileComponent", "config": {"avatarUrl": "{profile.avatarUrl}", "name": "{profile.name}", "email": "{profile.email}", "balance": "{profile.balance}", "buttons": [{"text": "Edit Profile", "navigation": "/public/profile-details"}, {"text": "View Transactions", "action": "viewTransactions"}]}}, {"type": "ButtonComponent", "config": {"text": "Go Home", "expand": "block", "fill": "solid", "color": "primary", "navigation": "/public/home"}}]}, {"title": "profile-details", "path": "profile-details", "secure": true, "class": "bg-green-500 h-full", "components": [{"type": "ProfileDetailsComponent", "config": {"title": "Update Profile Details", "profile": "{profile}"}}, {"type": "ButtonComponent", "config": {"text": "Back to Profile", "expand": "block", "fill": "solid", "color": "primary", "navigation": "/public/profile"}}]}, {"title": "login", "path": "login", "secure": false, "class": "bg-yellow-500 h-full", "components": [{"type": "PagesLoginTheme1Component", "config": {"title": "Login to your account", "logo": "assets/images/logo.png", "backgroundImage": "assets/images/login-bg.jpg", "registerLink": "/public/register"}}]}, {"title": "register", "path": "register", "secure": false, "class": "bg-purple-500 h-full", "components": [{"type": "HeadLogoComponent", "config": {"logo": "assets/images/logo.png", "title": "Register Account", "subtitle": "Join us now!"}}, {"type": "ButtonComponent", "config": {"text": "Already have an account? <PERSON>gin", "expand": "block", "fill": "clear", "color": "primary", "navigation": "/public/login"}}]}, {"title": "landing", "path": "landing", "secure": false, "class": "bg-base h-full", "components": [{"type": "PagesLandingTheme1Component", "showWhen": "authenticated", "inputs": {"kc": "kc", "profile": "profile", "router": "router", "logo": "assets/images/logo.png", "backgroundImage": "assets/images/background.jpg", "title": "Welcome to the Loyalty App", "subtitle": "Your rewards journey starts here.", "buttons": [{"text": "<PERSON><PERSON>", "navigation": "/public/login", "color": "primary", "fill": "solid"}, {"text": "Register", "navigation": "/public/register", "color": "secondary", "fill": "outline"}], "features": [{"icon": "star-outline", "title": "Earn Points", "description": "Get rewarded for every purchase."}, {"icon": "gift-outline", "title": "<PERSON><PERSON><PERSON>", "description": "Use points for exclusive benefits."}, {"icon": "flash-outline", "title": "Exclusive Offers", "description": "Access special deals and promotions."}]}}, {"type": "PagesLoginTheme1Component", "showWhen": "anonymous", "inputs": {"kc": "kc", "memberService": "memberService", "router": "router", "title": {"text": "Welcome", "class": "text-primaryC<PERSON>rast text-4xl text-center"}, "subtitle": {"text": "to Loyalty Plus App", "class": "text-primaryC<PERSON><PERSON>t text-center w-full"}, "icon": {"src": "assets/images/logo.png", "class": "rounded-large text-center w-80 mx-auto"}, "auth_buttons": {"class": "px-4 mt-6"}}}]}, {"title": "otp-validation", "path": "otp-validation", "secure": false, "class": "bg-gray-200 h-full flex items-center justify-center", "components": [{"type": "OtpValidatorComponent", "config": {"title": "Verify Your Identity", "subtitle": "Enter the OTP sent to your device", "successNavigation": "/public/home", "resendAction": "resendOtp"}}]}], "contact": {"callCenter": "012 141 3596", "email": ""}, "socials": {"facebook": "https://www.facebook.com/micahardware", "twitter": "https://twitter.com/micahardware", "linkedin": "https://www.linkedin.com/company/mica-hardware?originalSubdomain=za"}, "navigation": {"sidebarTitle": "Mica", "sidebarIcon": "assets/images/logo.png", "type": "sidebar", "routes": [{"id": "<PERSON><PERSON>", "label": "<PERSON><PERSON>", "link": "login", "icon": "log-in-outline", "active": true, "sidebar": false, "admin": false, "sort": 0, "exact": false}, {"id": "Register", "label": "Register", "link": "register", "icon": "person-add-outline", "active": true, "sidebar": false, "admin": false, "sort": 0, "exact": false}, {"id": "Logout", "label": "Logout", "link": "logout", "icon": "log-out-outline", "active": true, "sidebar": false, "admin": false, "sort": 0, "exact": false}, {"id": "Home", "label": "Home", "link": "/", "icon": "home", "active": true, "sidebar": true, "main": true, "admin": false, "sort": 0, "exact": true}, {"id": "Profile", "label": "Profile", "link": "/app/account", "icon": "person-outline", "active": true, "sidebar": true, "more": true, "admin": false, "sort": 0, "exact": false}, {"id": "Card", "label": "Card", "link": "/app/virtualcard", "icon": "card-outline", "active": true, "sidebar": true, "main": true, "admin": false, "sort": 0, "exact": false}, {"id": "Transactions", "label": "Transactions", "link": "/app/transactions", "icon": "card-outline", "active": true, "sidebar": true, "admin": false, "main": true, "sort": 0, "exact": false}, {"id": "Games", "label": "Games", "link": "/public/games/home", "icon": "game-controller-outline", "active": true, "sidebar": true, "admin": false, "more": true, "sort": 0}, {"id": "Stores", "label": "Stores", "link": "/app/stores", "icon": "location-outline", "active": true, "sidebar": true, "admin": false, "more": true, "sort": 0, "exact": false}, {"id": "Contact", "label": "Contact Us", "link": "/secure/contactus", "icon": "call-outline", "active": true, "sidebar": true, "admin": false, "more": true, "sort": 0, "exact": false}]}, "useDemoProfile": false, "version": 115, "client": "FFZ1", "env": "QA"}