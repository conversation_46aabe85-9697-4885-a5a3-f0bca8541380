/**
 * Custom stylesheet processor to fix ng-packagr stylesheet processing issue
 * This file exports the necessary handler function that's missing from the default processor
 */
const sass = require('sass');
const path = require('path');

/**
 * Process SCSS files during library build
 * @param {string} content - The content of the stylesheet
 * @param {string} filepath - The path to the stylesheet file
 * @returns {string} - The processed content
 */
function process(content, filepath) {
  const result = sass.compileString(content, {
    loadPaths: [path.dirname(filepath)],
    style: 'expanded',
  });
  return result.css;
}

// Export the handler function that ng-packagr is looking for
module.exports = {
  process
};
