#!/bin/bash

# List of component files to update
COMPONENTS=(
  "projects/lp-go/src/app/modules/dashboard/pages/notifications/notification-groups/notification-groups.component.ts"
  "projects/lp-go/src/app/modules/dashboard/pages/notifications/notification-history/notification-history.component.ts"
  "projects/lp-go/src/app/modules/dashboard/pages/notifications/notification-analytics/notification-analytics.component.ts"
  "projects/lp-go/src/app/modules/dashboard/pages/notifications/notification-template-form/notification-template-form.component.ts"
  "projects/lp-go/src/app/modules/dashboard/pages/notifications/notification-group-form/notification-group-form.component.ts"
  "projects/lp-go/src/app/modules/dashboard/pages/notifications/notification-send/notification-send.component.ts"
)

for component in "${COMPONENTS[@]}"; do
  echo "Updating $component..."
  
  # Check if the file exists
  if [ ! -f "$component" ]; then
    echo "File not found: $component"
    continue
  fi
  
  # Add CommonModule import and standalone: true to the component
  sed -i '' -e 's/import { Component, OnInit/import { Component, OnInit } from '"'"'@angular\/core'"'"';\
import { CommonModule } from '"'"'@angular\/common'"'"';\
import { FormsModule, ReactiveFormsModule } from '"'"'@angular\/forms'"'"';\
import { RouterModule/g' "$component"
  
  # Add standalone: true and imports to the @Component decorator
  sed -i '' -e 's/@Component({/@Component({\
  standalone: true,\
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule],/g' "$component"
  
  echo "Updated $component"
done

echo "All components updated successfully!"
