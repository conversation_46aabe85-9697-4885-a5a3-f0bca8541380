{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"lp-client": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/lp-client", "sourceRoot": "projects/lp-client/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/lp-client", "index": "projects/lp-client/src/index.html", "main": "projects/lp-client/src/main.ts", "polyfills": "projects/lp-client/src/polyfills.ts", "tsConfig": "projects/lp-client/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/lp-client/src/favicon.ico", "projects/lp-client/src/assets", "projects/lp-client/src/firebase-messaging-sw.js", {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["projects/lp-client/src/theme/theme.scss", "projects/lp-client/src/styles.css", {"input": "node_modules/@ionic/angular/css/core.css"}, {"input": "node_modules/@ionic/angular/css/normalize.css"}, {"input": "node_modules/@ionic/angular/css/structure.css"}, {"input": "node_modules/@ionic/angular/css/typography.css"}, {"input": "node_modules/@ionic/angular/css/display.css"}, {"input": "node_modules/@ionic/angular/css/padding.css"}, {"input": "node_modules/@ionic/angular/css/float-elements.css"}, {"input": "node_modules/@ionic/angular/css/text-alignment.css"}, {"input": "node_modules/@ionic/angular/css/text-transformation.css"}, {"input": "node_modules/@ionic/angular/css/flex-utils.css"}, {"input": "projects/lp-client/src/styles/flag-icons-wrapper.scss"}], "scripts": [], "allowedCommonJsDependencies": ["@babel/runtime"], "aot": true, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/environment.prod.ts"}], "outputHashing": "all", "aot": true, "buildOptimizer": true, "optimization": true, "vendorChunk": false, "extractLicenses": true, "sourceMap": false, "namedChunks": false}, "web-dev": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/environment.dev.web.ts"}]}, "web": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/environment.web.ts"}], "stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/default"]}}, "ffbp": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/ffbp"]}}, "rmic": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/rmic"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/dev/environment.rmic.dev.ts"}]}, "rmicdev": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/rmic"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/dev/environment.rmic.dev.ts"}]}, "ffz1": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/ffz1"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/dev/environment.ffz1.dev.ts"}]}, "ffz1dev": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/ffz1"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/dev/environment.ffz1.dev.ts"}]}, "ffz1qa": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/ffz1"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/qa/environment.ffz1.qa.ts"}]}, "rmicqa": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/rmic"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/qa/environment.rmic.qa.ts"}]}, "rmicprod": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/rmic"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/prod/environment.rmic.prod.ts"}]}, "gdem": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/gdem"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/dev/environment.gdem.dev.ts"}]}, "gdemdev": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/gdem"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/dev/environment.gdem.dev.ts"}]}, "rzia": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/rzia"]}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "lp-client:build:production"}, "web-dev": {"buildTarget": "lp-client:build:web-dev"}, "web": {"buildTarget": "lp-client:build:web"}, "ffbp": {"buildTarget": "lp-client:build:web-dev,ffbp"}, "rmic": {"buildTarget": "lp-client:build:web-dev,rmic"}, "ffz1": {"buildTarget": "lp-client:build:web-dev,ffz1"}, "ffz1dev": {"buildTarget": "lp-client:build:web-dev,ffz1"}, "ffz1qa": {"buildTarget": "lp-client:build:web-dev,ffz1qa"}, "rmicdev": {"buildTarget": "lp-client:build:web-dev,rmic"}, "rmicqa": {"buildTarget": "lp-client:build:web-dev,rmicqa"}, "rmicprod": {"buildTarget": "lp-client:build:production,rmicprod"}, "gdem": {"buildTarget": "lp-client:build:web-dev,gdem"}, "gdemdev": {"buildTarget": "lp-client:build:web-dev,gdem"}, "rzia": {"buildTarget": "lp-client:build:web-dev,rzia"}, "development": {"buildTarget": "lp-client:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "lp-client:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/lp-client/src/test.ts", "polyfills": "projects/lp-client/src/polyfills.ts", "tsConfig": "projects/lp-client/tsconfig.spec.json", "karmaConfig": "projects/lp-client/karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["projects/lp-client/src/favicon.ico", "projects/lp-client/src/assets", "projects/lp-client/src/firebase-messaging-sw.js", {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["projects/lp-client/src/styles.css"], "scripts": []}}, "ionic-cordova-serve": {"builder": "@ionic/angular-toolkit:cordova-serve", "options": {"cordovaBuildTarget": "lp-client:ionic-cordova-build", "devServerTarget": "lp-client:serve"}, "configurations": {"production": {"cordovaBuildTarget": "lp-client:ionic-cordova-build:production", "devServerTarget": "lp-client:serve:production"}}}, "ionic-cordova-build": {"builder": "@ionic/angular-toolkit:cordova-build", "options": {"buildTarget": "lp-client:build"}, "configurations": {"production": {"buildTarget": "lp-client:build:production"}}}}}, "lp-client-api": {"projectType": "library", "root": "projects/lp-client-api", "sourceRoot": "projects/lp-client-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/lp-client-api/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/lp-client-api/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/lp-client-api/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/lp-client-api/src/test.ts", "tsConfig": "projects/lp-client-api/tsconfig.spec.json", "karmaConfig": "projects/lp-client-api/karma.conf.js"}}}}, "mobile-components": {"projectType": "library", "root": "projects/mobile-components", "sourceRoot": "projects/mobile-components/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/mobile-components/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/mobile-components/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/mobile-components/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/mobile-components/src/test.ts", "tsConfig": "projects/mobile-components/tsconfig.spec.json", "karmaConfig": "projects/mobile-components/karma.conf.js"}}}}, "lp-terminal": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/lp-terminal", "sourceRoot": "projects/lp-terminal/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/lp-terminal", "index": "projects/lp-terminal/src/index.html", "main": "projects/lp-terminal/src/main.ts", "polyfills": "projects/lp-terminal/src/polyfills.ts", "tsConfig": "projects/lp-terminal/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/lp-terminal/src/favicon.ico", "projects/lp-terminal/src/assets", {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["projects/lp-terminal/src/theme/theme.scss", {"input": "node_modules/@ionic/angular/css/core.css"}, {"input": "node_modules/@ionic/angular/css/normalize.css"}, {"input": "node_modules/@ionic/angular/css/structure.css"}, {"input": "node_modules/@ionic/angular/css/typography.css"}, {"input": "node_modules/@ionic/angular/css/display.css"}, {"input": "node_modules/@ionic/angular/css/padding.css"}, {"input": "node_modules/@ionic/angular/css/float-elements.css"}, {"input": "node_modules/@ionic/angular/css/text-alignment.css"}, {"input": "node_modules/@ionic/angular/css/text-transformation.css"}, {"input": "node_modules/@ionic/angular/css/flex-utils.css"}, {"input": "projects/lp-terminal/src/theme/variables.css"}], "scripts": [], "allowedCommonJsDependencies": ["@babel/runtime"], "aot": true, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"dev": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "projects/lp-terminal/src/environments/environment.ts", "with": "projects/lp-terminal/src/environments/environment.dev.ts"}], "outputHashing": "all"}, "qa": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "projects/lp-terminal/src/environments/environment.ts", "with": "projects/lp-terminal/src/environments/environment.qa.ts"}], "outputHashing": "all"}, "prep": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "500kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "projects/lp-terminal/src/environments/environment.ts", "with": "projects/lp-terminal/src/environments/environment.prep.ts"}], "outputHashing": "all"}, "production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "projects/lp-terminal/src/environments/environment.ts", "with": "projects/lp-terminal/src/environments/environment.prod.ts"}], "outputHashing": "all", "aot": true, "buildOptimizer": true, "optimization": true, "vendorChunk": false, "extractLicenses": true, "sourceMap": false, "namedChunks": false}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"dev": {"buildTarget": "lp-terminal:build:dev"}, "qa": {"buildTarget": "lp-terminal:build:qa"}, "prep": {"buildTarget": "lp-terminal:build:prep"}, "production": {"buildTarget": "lp-terminal:build:production"}, "development": {"buildTarget": "lp-terminal:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "lp-terminal:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/lp-terminal/src/test.ts", "polyfills": "projects/lp-terminal/src/polyfills.ts", "tsConfig": "projects/lp-terminal/tsconfig.spec.json", "karmaConfig": "projects/lp-terminal/karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["projects/lp-terminal/src/favicon.ico", "projects/lp-terminal/src/assets", {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["projects/lp-terminal/src/styles.scss"], "scripts": []}}, "ionic-cordova-serve": {"builder": "@ionic/angular-toolkit:cordova-serve", "options": {"cordovaBuildTarget": "lp-terminal:ionic-cordova-build", "devServerTarget": "lp-terminal:serve"}, "configurations": {"production": {"cordovaBuildTarget": "lp-terminal:ionic-cordova-build:production", "devServerTarget": "lp-terminal:serve:production"}}}, "ionic-cordova-build": {"builder": "@ionic/angular-toolkit:cordova-build", "options": {"buildTarget": "lp-terminal:build"}, "configurations": {"production": {"buildTarget": "lp-terminal:build:production"}}}}}, "third-party-fix": {"projectType": "library", "root": "projects/third-party-fix", "sourceRoot": "projects/third-party-fix/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/third-party-fix/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/third-party-fix/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/third-party-fix/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "projects/third-party-fix/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}}}, "builder-test": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/builder-test", "sourceRoot": "projects/builder-test/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/builder-test", "index": "projects/builder-test/src/index.html", "main": "projects/builder-test/src/main.ts", "polyfills": "projects/builder-test/src/polyfills.ts", "tsConfig": "projects/builder-test/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/builder-test/src/favicon.ico", "projects/builder-test/src/assets"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "projects/builder-test/src/styles.scss"], "scripts": [], "allowedCommonJsDependencies": ["@babel/runtime"], "aot": true, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "projects/builder-test/src/environments/environment.ts", "with": "projects/builder-test/src/environments/environment.prod.ts"}], "outputHashing": "all", "aot": true, "buildOptimizer": true, "optimization": true, "vendorChunk": false, "extractLicenses": true, "sourceMap": false, "namedChunks": false}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "builder-test:build:production"}, "development": {"buildTarget": "builder-test:build:development"}}, "defaultConfiguration": "development"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/builder-test/src/test.ts", "polyfills": "projects/builder-test/src/polyfills.ts", "tsConfig": "projects/builder-test/tsconfig.spec.json", "karmaConfig": "projects/builder-test/karma.conf.js", "assets": ["projects/builder-test/src/favicon.ico", "projects/builder-test/src/assets"], "styles": ["projects/builder-test/src/styles.scss"], "scripts": []}}}}, "lp-client-test": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/lp-client-test", "sourceRoot": "projects/lp-client-test/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/lp-client-test", "index": "projects/lp-client-test/src/index.html", "main": "projects/lp-client-test/src/main.ts", "polyfills": "projects/lp-client-test/src/polyfills.ts", "tsConfig": "projects/lp-client-test/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/lp-client-test/src/favicon.ico", "projects/lp-client-test/src/assets", {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["projects/lp-client-test/src/theme/theme.scss", "projects/lp-client-test/src/styles.css", {"input": "node_modules/@ionic/angular/css/core.css"}, {"input": "node_modules/@ionic/angular/css/normalize.css"}, {"input": "node_modules/@ionic/angular/css/structure.css"}, {"input": "node_modules/@ionic/angular/css/typography.css"}, {"input": "node_modules/@ionic/angular/css/display.css"}, {"input": "node_modules/@ionic/angular/css/padding.css"}, {"input": "node_modules/@ionic/angular/css/float-elements.css"}, {"input": "node_modules/@ionic/angular/css/text-alignment.css"}, {"input": "node_modules/@ionic/angular/css/text-transformation.css"}, {"input": "node_modules/@ionic/angular/css/flex-utils.css"}, {"input": "projects/lp-client-test/src/styles/flag-icons-wrapper.scss"}], "scripts": [], "allowedCommonJsDependencies": ["@babel/runtime"], "aot": true, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "projects/lp-client-test/src/environments/environment.ts", "with": "projects/lp-client-test/src/environments/environment.prod.ts"}], "outputHashing": "all", "aot": true, "buildOptimizer": true, "optimization": true, "vendorChunk": false, "extractLicenses": true, "sourceMap": false, "namedChunks": false}, "ffz1dev": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client-test/src/theme", "projects/lp-client-test/src/theme/ffz1"]}, "fileReplacements": [{"replace": "projects/lp-client-test/src/environments/environment.ts", "with": "projects/lp-client-test/src/environments/dev/environment.ffz1.dev.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"port": 8100, "proxyConfig": "projects/lp-client-test/proxy.conf.json"}, "configurations": {"production": {"buildTarget": "lp-client-test:build:production"}, "ffz1dev": {"buildTarget": "lp-client-test:build:ffz1dev"}, "development": {"buildTarget": "lp-client-test:build:development"}}, "defaultConfiguration": "development"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/lp-client-test/src/test.ts", "polyfills": "projects/lp-client-test/src/polyfills.ts", "tsConfig": "projects/lp-client-test/tsconfig.spec.json", "karmaConfig": "projects/lp-client-test/karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["projects/lp-client-test/src/favicon.ico", "projects/lp-client-test/src/assets", {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["projects/lp-client-test/src/styles.css"], "scripts": []}}}}, "lp-go": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "css"}}, "root": "projects/lp-go", "sourceRoot": "projects/lp-go/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/lp-go", "index": "projects/lp-go/src/index.html", "main": "projects/lp-go/src/main.ts", "polyfills": "projects/lp-go/src/polyfills.ts", "tsConfig": "projects/lp-go/tsconfig.app.json", "inlineStyleLanguage": "css", "assets": ["projects/lp-go/src/favicon.ico", "projects/lp-go/src/assets"], "styles": ["projects/lp-go/src/styles.css"], "scripts": [], "aot": true, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "projects/lp-go/src/environments/environment.ts", "with": "projects/lp-go/src/environments/environment.prod.ts"}], "outputHashing": "all", "aot": true, "buildOptimizer": true, "optimization": true, "vendorChunk": false, "extractLicenses": true, "sourceMap": false, "namedChunks": false}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "lp-go:build:production"}, "development": {"buildTarget": "lp-go:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "lp-go:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/lp-go/src/test.ts", "polyfills": "projects/lp-go/src/polyfills.ts", "tsConfig": "projects/lp-go/tsconfig.spec.json", "karmaConfig": "projects/lp-go/karma.conf.js", "assets": ["projects/lp-go/src/favicon.ico", "projects/lp-go/src/assets"], "styles": ["projects/lp-go/src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": "d5b6024d-c32c-4c49-ba86-920959896666"}}