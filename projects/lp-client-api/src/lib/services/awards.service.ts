import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { delay } from 'rxjs/operators';
import { 
  AwardsUser, 
  OtpRequest, 
  OtpVerification, 
  RegistrationRequest, 
  RegistrationResponse,
  LoginRequest,
  LoginResponse,
  AwardsTransaction,
  UserBalance,
  BalanceResponse,
  PartnerCard,
  CardResponse,
  EnrollmentRequest,
  EnrollmentResponse,
  TransactionRequest,
  TransactionResponse
} from '../types/awards';

@Injectable({
  providedIn: 'root'
})
export class AwardsService {
  private mockUsers: AwardsUser[] = [];
  private otpStore: Map<string, string> = new Map();
  private mockBalances: Map<string, UserBalance[]> = new Map();
  private mockTransactions: Map<string, AwardsTransaction[]> = new Map();
  private mockCards: Map<string, PartnerCard[]> = new Map();
  private currentUser: AwardsUser | null = null;
  
  private partners = [
    {
      id: 'pna',
      name: 'Pick n Pay',
      logo: 'P<PERSON>',
      backgroundColor: '#e53935'
    },
    {
      id: 'buildit',
      name: 'Build It',
      logo: 'Build IT',
      backgroundColor: '#c62828'
    },
    {
      id: 'leroymerlin',
      name: '<PERSON> <PERSON>',
      logo: 'LEROY MERLIN',
      backgroundColor: '#66bb6a'
    }
  ];

  constructor() {
    this.initializeMockData();
  }

  private initializeMockData(): void {
    // Create a default test user
    const testUser: AwardsUser = {
      mobile: '0123456789',
      name: 'Marius',
      surname: 'Test',
      email: '<EMAIL>',
      dateOfBirth: '1990-01-01',
      idNumber: '9001015800084',
      password: 'password123'
    };
    this.mockUsers.push(testUser);

    // Initialize balances for partners
    const userId = 'USER_DEFAULT';
    const balances: UserBalance[] = [
      {
        userId,
        partnerId: 'pna',
        balance: 50336,
        currency: 'R',
        lastUpdated: new Date().toISOString()
      },
      {
        userId,
        partnerId: 'buildit',
        balance: 12500,
        currency: 'R',
        lastUpdated: new Date().toISOString()
      },
      {
        userId,
        partnerId: 'leroymerlin',
        balance: 8750,
        currency: 'R',
        lastUpdated: new Date().toISOString()
      }
    ];
    this.mockBalances.set(userId, balances);

    // Initialize transactions
    const transactions: AwardsTransaction[] = [
      {
        id: '1',
        userId,
        partnerId: 'pna',
        date: '05/01/2025',
        description: 'Purchase',
        amount: -2500.00,
        type: 'debit',
        currency: 'R'
      },
      {
        id: '2',
        userId,
        partnerId: 'pna',
        date: '10/01/2025',
        description: 'Purchase',
        amount: -1200.00,
        type: 'debit',
        currency: 'R'
      },
      {
        id: '3',
        userId,
        partnerId: 'pna',
        date: '20/01/2025',
        description: 'Purchase',
        amount: -3100.00,
        type: 'debit',
        currency: 'R'
      },
      {
        id: '4',
        userId,
        partnerId: 'pna',
        date: '31/01/2025',
        description: 'Rewards Credit',
        amount: 6109.00,
        type: 'credit',
        currency: 'R'
      }
    ];
    this.mockTransactions.set(userId, transactions);
    
    // Initialize cards
    const cards: PartnerCard[] = [
      {
        userId,
        partnerId: 'pna',
        cardNumber: '1237896114',
        isActive: true,
        createdAt: new Date().toISOString()
      },
      {
        userId,
        partnerId: 'buildit',
        cardNumber: '1237896115',
        isActive: true,
        createdAt: new Date().toISOString()
      },
      {
        userId,
        partnerId: 'leroymerlin',
        cardNumber: '1237896116',
        isActive: true,
        createdAt: new Date().toISOString()
      }
    ];
    this.mockCards.set(userId, cards);
  }

  requestOtp(request: OtpRequest): Observable<{ success: boolean; message: string }> {
    const mockOtp = Math.floor(100000 + Math.random() * 900000).toString();
    this.otpStore.set(request.mobile, mockOtp);
    
    console.log(`Mock OTP for ${request.mobile}: ${mockOtp}`);
    
    return of({ 
      success: true, 
      message: 'OTP sent successfully' 
    }).pipe(delay(1000));
  }

  verifyOtp(verification: OtpVerification): Observable<{ success: boolean; message: string }> {
    const storedOtp = this.otpStore.get(verification.mobile);
    
    if (!storedOtp) {
      return throwError(() => new Error('No OTP found for this mobile number')).pipe(delay(500));
    }
    
    if (storedOtp !== verification.otp) {
      return throwError(() => new Error('Invalid OTP')).pipe(delay(500));
    }
    
    return of({ 
      success: true, 
      message: 'OTP verified successfully' 
    }).pipe(delay(1000));
  }

  register(registration: RegistrationRequest): Observable<RegistrationResponse> {
    const storedOtp = this.otpStore.get(registration.mobile);
    
    if (!storedOtp || storedOtp !== registration.otp) {
      return throwError(() => new Error('Invalid OTP')).pipe(delay(500));
    }
    
    const newUser: AwardsUser = {
      mobile: registration.mobile,
      name: registration.name,
      surname: registration.surname,
      email: registration.email,
      dateOfBirth: registration.dateOfBirth,
      idNumber: registration.idNumber,
      password: 'defaultPassword123' // In real app, user would set this
    };
    
    this.mockUsers.push(newUser);
    this.otpStore.delete(registration.mobile);
    
    const userId = `USER_${Date.now()}`;
    
    // Initialize empty balances for new user
    this.mockBalances.set(userId, [
      {
        userId,
        partnerId: 'pna',
        balance: 0,
        currency: 'R',
        lastUpdated: new Date().toISOString()
      },
      {
        userId,
        partnerId: 'buildit',
        balance: 0,
        currency: 'R',
        lastUpdated: new Date().toISOString()
      },
      {
        userId,
        partnerId: 'leroymerlin',
        balance: 0,
        currency: 'R',
        lastUpdated: new Date().toISOString()
      }
    ]);
    
    this.mockTransactions.set(userId, []);
    
    // Initialize cards for new user
    const cards: PartnerCard[] = this.partners.map((partner, index) => ({
      userId,
      partnerId: partner.id,
      cardNumber: `${Date.now()}${index}`,
      isActive: true,
      createdAt: new Date().toISOString()
    }));
    this.mockCards.set(userId, cards);
    
    return of({
      success: true,
      message: 'Registration successful',
      userId
    }).pipe(delay(1500));
  }

  login(request: LoginRequest): Observable<LoginResponse> {
    const user = this.mockUsers.find(u => u.mobile === request.mobile);
    
    if (!user) {
      return throwError(() => new Error('User not found')).pipe(delay(500));
    }
    
    if (user.password !== request.password) {
      return throwError(() => new Error('Invalid password')).pipe(delay(500));
    }
    
    this.currentUser = user;
    
    return of({
      success: true,
      message: 'Login successful',
      user,
      token: `TOKEN_${Date.now()}`
    }).pipe(delay(1000));
  }

  getBalance(userId: string, partnerId: string): Observable<BalanceResponse> {
    const userBalances = this.mockBalances.get(userId) || [];
    const balance = userBalances.find(b => b.partnerId === partnerId);
    
    if (!balance) {
      return throwError(() => new Error('Balance not found')).pipe(delay(500));
    }
    
    const userTransactions = this.mockTransactions.get(userId) || [];
    const partnerTransactions = userTransactions.filter(t => t.partnerId === partnerId);
    
    return of({
      balance,
      transactions: partnerTransactions
    }).pipe(delay(800));
  }

  getAllBalances(userId: string): Observable<UserBalance[]> {
    const balances = this.mockBalances.get(userId) || [];
    return of(balances).pipe(delay(500));
  }

  getCurrentUser(): AwardsUser | null {
    return this.currentUser;
  }

  logout(): void {
    this.currentUser = null;
  }

  getUsers(): Observable<AwardsUser[]> {
    return of(this.mockUsers).pipe(delay(500));
  }
  
  getCard(userId: string, partnerId: string): Observable<CardResponse> {
    const userCards = this.mockCards.get(userId) || [];
    const card = userCards.find(c => c.partnerId === partnerId);
    
    if (!card) {
      return throwError(() => new Error('Card not found')).pipe(delay(500));
    }
    
    const user = this.mockUsers.find(u => u.mobile === this.currentUser?.mobile);
    const partner = this.partners.find(p => p.id === partnerId);
    
    if (!user || !partner) {
      return throwError(() => new Error('User or partner not found')).pipe(delay(500));
    }
    
    return of({
      card,
      user,
      partner
    }).pipe(delay(500));
  }
  
  getAllCards(userId: string): Observable<PartnerCard[]> {
    const cards = this.mockCards.get(userId) || [];
    return of(cards).pipe(delay(500));
  }
  
  getPartners(): Observable<typeof this.partners> {
    return of(this.partners).pipe(delay(300));
  }
  
  enrollInProgram(request: EnrollmentRequest): Observable<EnrollmentResponse> {
    if (!request.acceptedTerms) {
      return throwError(() => new Error('Terms must be accepted')).pipe(delay(500));
    }
    
    // Check if already enrolled
    const userCards = this.mockCards.get(request.userId) || [];
    const existingCard = userCards.find(c => c.partnerId === request.partnerId);
    
    if (existingCard && existingCard.isActive) {
      return throwError(() => new Error('Already enrolled in this program')).pipe(delay(500));
    }
    
    // Create or update card
    const memberNumber = `${Date.now()}${Math.floor(Math.random() * 1000)}`;
    
    if (existingCard) {
      existingCard.isActive = true;
    } else {
      const newCard: PartnerCard = {
        userId: request.userId,
        partnerId: request.partnerId,
        cardNumber: memberNumber,
        isActive: true,
        createdAt: new Date().toISOString()
      };
      userCards.push(newCard);
      this.mockCards.set(request.userId, userCards);
    }
    
    return of({
      success: true,
      message: 'Successfully enrolled in program',
      memberNumber,
      card: existingCard || userCards[userCards.length - 1]
    }).pipe(delay(1500));
  }
  
  processTransaction(request: TransactionRequest): Observable<TransactionResponse> {
    const userBalances = this.mockBalances.get(request.userId) || [];
    const balance = userBalances.find(b => b.partnerId === request.partnerId);
    
    if (!balance) {
      return throwError(() => new Error('User not enrolled in this program')).pipe(delay(500));
    }
    
    const userTransactions = this.mockTransactions.get(request.userId) || [];
    
    if (request.type === 'redemption') {
      // Calculate points needed (1 point = R1)
      const pointsNeeded = Math.ceil(request.amount);
      
      if (balance.balance < pointsNeeded) {
        return of({
          success: false,
          message: 'Insufficient points for redemption',
          newBalance: balance.balance
        }).pipe(delay(1000));
      }
      
      // Process redemption
      balance.balance -= pointsNeeded;
      balance.lastUpdated = new Date().toISOString();
      
      const transaction: AwardsTransaction = {
        id: `TXN_${Date.now()}`,
        userId: request.userId,
        partnerId: request.partnerId,
        date: new Date().toLocaleDateString('en-GB'),
        description: 'Points Redemption',
        amount: -request.amount,
        type: 'debit',
        currency: 'R'
      };
      
      userTransactions.push(transaction);
      this.mockTransactions.set(request.userId, userTransactions);
      
      return of({
        success: true,
        message: 'Redemption successful',
        transaction,
        pointsUsed: pointsNeeded,
        newBalance: balance.balance
      }).pipe(delay(1500));
    } else {
      // Process accrual (R100 = 10 points)
      const pointsEarned = Math.floor(request.amount / 10);
      balance.balance += pointsEarned;
      balance.lastUpdated = new Date().toISOString();
      
      const transaction: AwardsTransaction = {
        id: `TXN_${Date.now()}`,
        userId: request.userId,
        partnerId: request.partnerId,
        date: new Date().toLocaleDateString('en-GB'),
        description: 'Points Earned',
        amount: pointsEarned,
        type: 'credit',
        currency: 'Points'
      };
      
      userTransactions.push(transaction);
      this.mockTransactions.set(request.userId, userTransactions);
      
      return of({
        success: true,
        message: 'Points accrued successfully',
        transaction,
        pointsEarned,
        newBalance: balance.balance
      }).pipe(delay(1500));
    }
  }
}