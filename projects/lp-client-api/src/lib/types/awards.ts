export interface AwardsUser {
  mobile: string;
  otp?: string;
  name: string;
  surname: string;
  email: string;
  dateOfBirth: string;
  idNumber: string;
  password?: string;
}

export interface OtpRequest {
  mobile: string;
}

export interface OtpVerification {
  mobile: string;
  otp: string;
}

export interface RegistrationRequest {
  mobile: string;
  otp: string;
  name: string;
  surname: string;
  email: string;
  dateOfBirth: string;
  idNumber: string;
}

export interface RegistrationResponse {
  success: boolean;
  message: string;
  userId?: string;
}

export interface LoginRequest {
  mobile: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  user?: AwardsUser;
  token?: string;
}

export interface AwardsTransaction {
  id: string;
  userId: string;
  partnerId: string;
  date: string;
  description: string;
  amount: number;
  type: 'credit' | 'debit';
  currency: string;
}

export interface UserBalance {
  userId: string;
  partnerId: string;
  balance: number;
  currency: string;
  lastUpdated: string;
}

export interface BalanceResponse {
  balance: UserBalance;
  transactions: AwardsTransaction[];
}

export interface PartnerCard {
  userId: string;
  partnerId: string;
  cardNumber: string;
  isActive: boolean;
  createdAt: string;
}

export interface CardResponse {
  card: PartnerCard;
  user: AwardsUser;
  partner: {
    id: string;
    name: string;
    logo: string;
    backgroundColor: string;
  };
}

export interface EnrollmentRequest {
  userId: string;
  partnerId: string;
  acceptedTerms: boolean;
}

export interface EnrollmentResponse {
  success: boolean;
  message: string;
  memberNumber: string;
  card?: PartnerCard;
}

export interface TransactionRequest {
  userId: string;
  partnerId: string;
  type: 'accrual' | 'redemption';
  amount: number;
  items?: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
}

export interface TransactionResponse {
  success: boolean;
  message: string;
  transaction?: AwardsTransaction;
  pointsEarned?: number;
  pointsUsed?: number;
  newBalance?: number;
}