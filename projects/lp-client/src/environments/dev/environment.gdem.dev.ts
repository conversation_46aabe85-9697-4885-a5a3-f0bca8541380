// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  env: 'DEV',
  client: 'gdem',
  lssConfig: {
    googleApiKey: 'AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0',
    apiId: '812275411',
    apiIdKeyStart: 'ITMANQA_038958288_START',
    apiIdKeyEnd: 'ITMANQA_039785672_END',
    appCode: 'gdem',
    appName: 'GDEM Awards',
    appVersion: '0.0.12',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    defaultLat: -28.83688693522886,
    defaultLng: 25.49975999318031,
    loadIdentity: false,
    identityBaseUrl: 'http://payroll.dv.lss.si/servlet/systemImage',
    appBaseUrl: 'http://alpine',
    apiBaseUrl: 'https://ffz1dev.loyaltyplus.aero/',
    logEndpoint: 'https://ffz1dev.loyaltyplus.aero/extsecure/tools/logservice',
    configAPIUrl: 'http://{hostname}/config/api/v1/',
    termsConditions: 'http://payroll.dv.lss.si/servlet/systemImage',
    icon: '/assets/images/logo.png',
    contact: {
      callCenter: '************',
    },
    pointsExpire: false,
    pointsTitle: 'Points',
    memberPhone: {
      dialCode: '+27',
      nationalNumber: '',
    },
    memberCard: '',

    telephone: {
      selectFirstCountry: true,
      preferredCountries: ['za'],
      onlyCountries: ['za', 'ls', 'bw', 'na', 'sz', 'mz'],
    },
     socials: {
      instagram: '',
      facebook: 'https://www.facebook.com/LoyaltyPlusSA/',
      twitter: 'https://twitter.com/LoyaltyPlusSA',
      linkedin:
        'https://www.linkedin.com/company/loyaltyplus-accolades-pty-ltd?trk=biz-companies-cym',
    },
    navigation: {
      sidebarTitle: 'GDEM Awards',
      sidebarIcon: 'assets/images/logo.png',
      type: 'sidebar',
      routes: [
        {
          path: '/public/awards/login',
          label: 'Login',
          icon: 'log-in-outline',
          main: true,
          sidebar: true,
          more: false
        },
        {
          path: '/public/awards/dashboard',
          label: 'Dashboard',
          icon: 'home-outline',
          main: true,
          sidebar: true,
          more: false
        },
        {
          path: '/public/awards/balance',
          label: 'Points Balance',
          icon: 'wallet-outline',
          main: true,
          sidebar: true,
          more: false
        },
        {
          path: '/public/awards/cards',
          label: 'My Cards',
          icon: 'card-outline',
          main: true,
          sidebar: true,
          more: false
        },
        {
          path: '/public/awards/transactions',
          label: 'Transactions',
          icon: 'list-outline',
          main: true,
          sidebar: true,
          more: false
        },
        {
          path: '/public/awards/transfer',
          label: 'Transfer Points',
          icon: 'swap-horizontal-outline',
          main: false,
          sidebar: true,
          more: true
        },
        {
          path: '/public/awards/partner-enrollment',
          label: 'Enroll Partners',
          icon: 'business-outline',
          main: false,
          sidebar: true,
          more: true
        },
        {
          id: 'Logout',
          label: 'Logout',
          link: 'logout',
          icon: 'log-out-outline',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
          exact: false,
        },
      ],
    },
    pages: [
      {
        title: 'awards-landing',
        path: 'awards',
        secure: false,
        class: 'h-full',
        components: [
          {
            type: 'RedirectComponent',
            config: {
              redirectTo: '/public/awards/login'
            }
          }
        ]
      }
    ],
    authConfig: {
      issuer: 'https://authdev.loyaltyplus.aero/auth/realms/DemoZ1',
      clientId: 'mobile-app',
      logoutUrl: '/',
      url: 'https://authdev.loyaltyplus.aero/auth',
      realm: 'DemoZ1',
      initOptions: {
        adapter: 'default',
        responseType: 'code',
        scope: 'openid profile email offline_access',
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri:
          window.location.origin.replace('.aero', '.aero/lp-mobile') +
          '/assets/silent-check-sso.html',
      },
    },
    useDemoProfile: true,

  },
};
