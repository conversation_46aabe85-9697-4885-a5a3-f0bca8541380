import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PublicRoutingModule } from './public-routing.module';
import { IonicModule } from '@ionic/angular';
import { AwardsModule } from '@projects/mobile-components';
import { HomeComponent } from './home/<USER>';
import { LandingComponent } from './landing/landing.component';
import { LoginComponent } from './login/login.component';
import { SignupComponent } from './signup/signup.component';
import { PasswordComponent } from './password/password.component';
import { NotificationsComponent } from './notifications/notifications.component';
import { StoresComponent } from './stores/stores.component';
import { StoreDetailComponent } from './store-detail/store-detail.component';
import { ValidateComponent } from './validate/validate.component';
import { OtpComponent } from './otp/otp.component';
import { CategoriesComponent } from './games/categories/categories.component';
import { SingleComponent } from './games/single/single.component';
import { GamesHomeComponent } from './games/home/<USER>';
import { AllGamesComponent } from './games/all/all.component';
import { DashboardComponent } from './games/dashboard/dashboard.component';
import { HowToPlayComponent } from './games/how-to-play/how-to-play.component';
import { FavouritesComponent } from './games/favourites/favourites.component';

@NgModule({
  imports: [
    CommonModule,
    PublicRoutingModule,
    IonicModule,
    AwardsModule,
    HomeComponent,
    LandingComponent,
    LoginComponent,
    SignupComponent,
    PasswordComponent,
    NotificationsComponent,
    StoresComponent,
    StoreDetailComponent,
    ValidateComponent,
    OtpComponent,
    CategoriesComponent,
    SingleComponent,
    GamesHomeComponent,
    AllGamesComponent,
    DashboardComponent,
    HowToPlayComponent,
    FavouritesComponent
  ]
})
export class PublicModule { }
