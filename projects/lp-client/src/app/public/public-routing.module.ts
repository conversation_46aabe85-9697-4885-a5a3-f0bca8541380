import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AppTabsPage } from './app-tabs/app-tabs.page';
import { LandingComponent } from './landing/landing.component';
import { LoginComponent } from './login/login.component';
import { NotificationsComponent } from './notifications/notifications.component';
import { PasswordComponent } from './password/password.component';
import { SignupComponent } from './signup/signup.component';
import { StoreDetailComponent } from './store-detail/store-detail.component';
import { StoresComponent } from './stores/stores.component';
import { OtpComponent } from './otp/otp.component';
import { ValidateComponent } from './validate/validate.component';
import { CategoriesComponent } from './games/categories/categories.component';
import { SingleComponent } from './games/single/single.component';
import { GamesHomeComponent } from './games/home/<USER>';
import { VirtualCardsComponent } from './virtual/virtual.component';
import { AllGamesComponent } from './games/all/all.component';
import { DashboardComponent } from './games/dashboard/dashboard.component';
import { HowToPlayComponent } from './games/how-to-play/how-to-play.component';
import { FavouritesComponent } from './games/favourites/favourites.component';
import { ChatComponent } from './chat/chat.component';

// Awards imports
import { AwardsOtpRequestComponent } from './awards/otp-request/otp-request.component';
import { AwardsOtpVerificationComponent } from './awards/otp-verification/otp-verification.component';
import { AwardsRegistrationComponent } from './awards/registration/registration.component';
import { AwardsLoginComponent } from './awards/login/login.component';
import { AwardsDashboardComponent } from './awards/dashboard/dashboard.component';
import { AwardsBalanceDetailComponent } from './awards/balance-detail/balance-detail.component';
import { AwardsPartnerCardComponent } from './awards/partner-card/partner-card.component';
import { AwardsQrCardComponent } from './awards/qr-card/qr-card.component';
import { AwardsProgramSelectionComponent } from './awards/program-selection/program-selection.component';
import { AwardsTermsAcceptanceComponent } from './awards/terms-acceptance/terms-acceptance.component';
import { AwardsEnrollmentConfirmationComponent } from './awards/enrollment-confirmation/enrollment-confirmation.component';
import { AwardsTransactionComponent } from './awards/transaction/transaction.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
  {
    path: 'app',
    component: AppTabsPage, // This is the default non-authenticated route
  },
  {
    path: 'landing',
    component: LandingComponent,
  },
  {
    path: 'login',
    component: LoginComponent,
  },
  {
    path: 'signup',
    component: SignupComponent,
  },
  {
    path: 'password',
    component: PasswordComponent,
    pathMatch: 'full',
  },
  {
    path: 'notifications',
    component: NotificationsComponent,
    pathMatch: 'full',
  },
  {
    path: 'stores',
    component: StoresComponent,
    pathMatch: 'full',
  },
  {
    path: 'storedetail',
    component: StoreDetailComponent,
    pathMatch: 'full',
  },
  {
    path: 'otp',
    component: OtpComponent,
    pathMatch: 'full',
  },
  {
    path: 'validate',
    component: ValidateComponent,
    pathMatch: 'full',
  },
  {
    path: 'games/categories',
    component: CategoriesComponent,
  },
  {
    path: 'games/single',
    component: SingleComponent,
    pathMatch: 'full',
  },
  {
    path: 'games/home',
    component: GamesHomeComponent,
    pathMatch: 'full',
  },
  {
    path: 'virtual',
    component: VirtualCardsComponent,
    pathMatch: 'full',
  },
  {
    path: 'games/all',
    component: AllGamesComponent,
    pathMatch: 'full',
  },
  {
    path: 'games/dashboard',
    component: DashboardComponent,
    pathMatch: 'full',
  },
  {
    path: 'games/how-to-play',
    component: HowToPlayComponent,
    pathMatch: 'full',
  },
  {
    path: 'games/favourites',
    component: FavouritesComponent,
    pathMatch: 'full',
  },
  {
    path: 'chat',
    component: ChatComponent,
    pathMatch: 'full',
  },
  // Awards routes
  {
    path: 'awards',
    children: [
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      },
      {
        path: 'otp-request',
        component: AwardsOtpRequestComponent
      },
      {
        path: 'otp-verification',
        component: AwardsOtpVerificationComponent
      },
      {
        path: 'registration',
        component: AwardsRegistrationComponent
      },
      {
        path: 'login',
        component: AwardsLoginComponent
      },
      {
        path: 'dashboard',
        component: AwardsDashboardComponent
      },
      {
        path: 'balance-detail',
        component: AwardsBalanceDetailComponent
      },
      {
        path: 'partner-card',
        component: AwardsPartnerCardComponent
      },
      {
        path: 'qr-card',
        component: AwardsQrCardComponent
      },
      {
        path: 'program-selection',
        component: AwardsProgramSelectionComponent
      },
      {
        path: 'terms-acceptance',
        component: AwardsTermsAcceptanceComponent
      },
      {
        path: 'enrollment-confirmation',
        component: AwardsEnrollmentConfirmationComponent
      },
      {
        path: 'transaction',
        component: AwardsTransactionComponent
      }
    ]
  }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PublicRoutingModule {}
