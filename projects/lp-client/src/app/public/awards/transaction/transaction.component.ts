import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { TransactionFlowComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-transaction',
  standalone: true,
  imports: [CommonModule, TransactionFlowComponent],
  template: `
    <app-transaction-flow
      [userId]="userId"
      [partnerId]="partnerId"
      [transactionType]="transactionType"
      (navigate)="onNavigate($event)">
    </app-transaction-flow>
  `,
  styles: []
})
export class AwardsTransactionComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';
  transactionType: 'accrual' | 'redemption' = 'accrual';

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || 'pna';
      this.transactionType = params['type'] || 'accrual';
    });
  }

  onNavigate(route: string) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card']);
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      default:
        break;
    }
  }
}