import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { TransactionFlowComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-transaction',
  standalone: true,
  imports: [CommonModule, TransactionFlowComponent],
  template: `
    <app-transaction-flow
      [transactionInfo]="transactionInfo"
      (navigationClicked)="onNavigate($event)"
      (qrScanned)="onQrScanned()">
    </app-transaction-flow>
  `,
  styles: []
})
export class AwardsTransactionComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';
  transactionType: 'accrual' | 'redemption' = 'accrual';

  transactionInfo = {
    type: 'accrual' as 'accrual' | 'redemption',
    partnerId: 'buildit',
    partnerName: 'Build It',
    partnerLogo: 'Build IT',
    backgroundColor: '#c62828',
    userName: '<PERSON>',
    memberNumber: '1237896114',
    pointsRate: 'R100 gives 10 points'
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || 'pna';
      this.transactionType = params['type'] || 'accrual';
      // Update transactionInfo based on parameters
      this.transactionInfo = {
        ...this.transactionInfo,
        type: this.transactionType,
        partnerId: this.partnerId,
        partnerName: this.getPartnerName(this.partnerId)
      };
    });
  }

  private getPartnerName(partnerId: string): string {
    const partnerNames: { [key: string]: string } = {
      'pna': 'Pick n Pay',
      'buildit': 'Build It',
      'leroymerlin': 'Leroy Merlin',
      'default': 'Partner'
    };
    return partnerNames[partnerId] || partnerNames['default'];
  }

  onQrScanned() {
    // Handle QR scan completion
    console.log('QR Code scanned for transaction');
  }

  onNavigate(route: string) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card']);
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      default:
        break;
    }
  }
}