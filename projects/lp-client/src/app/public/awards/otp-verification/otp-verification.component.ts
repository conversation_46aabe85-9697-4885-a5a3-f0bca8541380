import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { OtpVerificationComponent as MobileOtpVerificationComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-otp-verification',
  standalone: true,
  imports: [CommonModule, MobileOtpVerificationComponent],
  template: `
    <app-otp-verification 
      [mobile]="mobile"
      (otpVerified)="onOtpVerified($event)"
      (navigateBack)="onNavigateBack()">
    </app-otp-verification>
  `,
  styles: []
})
export class AwardsOtpVerificationComponent implements OnInit {
  mobile = '';

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.mobile = params['mobile'] || '';
    });
  }

  onOtpVerified(data: { mobile: string; otp: string }) {
    this.router.navigate(['/public/awards/registration'], {
      queryParams: { mobile: data.mobile, otp: data.otp }
    });
  }

  onNavigateBack() {
    this.router.navigate(['/public/awards/otp-request']);
  }
}