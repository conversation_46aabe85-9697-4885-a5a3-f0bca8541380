import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { PartnerCardComponent as MobilePartnerCardComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-partner-card',
  standalone: true,
  imports: [CommonModule, MobilePartnerCardComponent],
  template: `
    <app-partner-card
      [userId]="userId"
      [partnerId]="partnerId"
      (useCard)="onUseCard($event)"
      (navigate)="onNavigate($event)">
    </app-partner-card>
  `,
  styles: []
})
export class AwardsPartnerCardComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || 'pna';
    });
  }

  onUseCard(partnerId: string) {
    this.router.navigate(['/public/awards/qr-card'], {
      queryParams: { partnerId }
    });
  }

  onNavigate(route: string) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}