import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { PartnerCardComponent as MobilePartnerCardComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-partner-card',
  standalone: true,
  imports: [CommonModule, MobilePartnerCardComponent],
  template: `
    <app-partner-card
      [cardInfo]="cardInfo"
      (navigationClicked)="onNavigate($event)"
      (cardClicked)="onUseCard($event)">
    </app-partner-card>
  `,
  styles: []
})
export class AwardsPartnerCardComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';

  cardInfo = {
    partnerId: 'buildit',
    partnerName: 'Build It',
    partnerLogo: 'Build IT',
    backgroundColor: '#c62828',
    userName: 'Marius',
    balance: 2116,
    currency: 'R',
    transactions: []
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || 'pna';
      // Update cardInfo based on partnerId
      this.cardInfo = {
        ...this.cardInfo,
        partnerId: this.partnerId,
        partnerName: this.getPartnerName(this.partnerId)
      };
    });
  }

  private getPartnerName(partnerId: string): string {
    const partnerNames: { [key: string]: string } = {
      'pna': 'PNA',
      'buildit': 'Build It',
      'default': 'Partner'
    };
    return partnerNames[partnerId] || partnerNames['default'];
  }

  onUseCard(event: any) {
    this.router.navigate(['/public/awards/qr-card'], {
      queryParams: { partnerId: this.partnerId }
    });
  }

  onNavigate(route: any) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}