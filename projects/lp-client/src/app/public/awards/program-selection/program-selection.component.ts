import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ProgramSelectionComponent as MobileProgramSelectionComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-program-selection',
  standalone: true,
  imports: [CommonModule, MobileProgramSelectionComponent],
  template: `
    <app-program-selection
      (programSelected)="onSelectProgram($event)"
      (navigationClicked)="onNavigate($event)">
    </app-program-selection>
  `,
  styles: []
})
export class AwardsProgramSelectionComponent {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';

  constructor(private router: Router) {}

  onSelectProgram(programId: string) {
    this.router.navigate(['/public/awards/terms-acceptance'], {
      queryParams: { partnerId: programId }
    });
  }

  onNavigate(route: any) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}