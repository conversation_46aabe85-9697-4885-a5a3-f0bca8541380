import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { QrCardComponent as MobileQrCardComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-qr-card',
  standalone: true,
  imports: [CommonModule, MobileQrCardComponent],
  template: `
    <app-qr-card
      [cardInfo]="cardInfo"
      (navigationClicked)="onNavigate($event)"
      (backClicked)="onBack()">
    </app-qr-card>
  `,
  styles: []
})
export class AwardsQrCardComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';

  cardInfo = {
    partnerId: 'buildit',
    partnerName: 'Build It',
    partnerLogo: 'Build IT',
    backgroundColor: '#c62828',
    userName: '<PERSON>',
    userNumber: '1237896114'
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || 'pna';
      // Update cardInfo based on partnerId
      this.cardInfo = {
        ...this.cardInfo,
        partnerId: this.partnerId,
        partnerName: this.getPartnerName(this.partnerId)
      };
    });
  }

  private getPartnerName(partnerId: string): string {
    const partnerNames: { [key: string]: string } = {
      'pna': 'PNA',
      'buildit': 'Build It',
      'default': 'Partner'
    };
    return partnerNames[partnerId] || partnerNames['default'];
  }

  onBack() {
    this.router.navigate(['/public/awards/partner-card'], {
      queryParams: { partnerId: this.partnerId }
    });
  }

  onNavigate(route: any) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card']);
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}