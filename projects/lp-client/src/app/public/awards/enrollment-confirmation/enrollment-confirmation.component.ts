import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { EnrollmentConfirmationComponent as MobileEnrollmentConfirmationComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-enrollment-confirmation',
  standalone: true,
  imports: [CommonModule, MobileEnrollmentConfirmationComponent],
  template: `
    <app-enrollment-confirmation
      [userId]="userId"
      [partnerId]="partnerId"
      (continue)="onContinue()">
    </app-enrollment-confirmation>
  `,
  styles: []
})
export class AwardsEnrollmentConfirmationComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || '';
    });
  }

  onContinue() {
    this.router.navigate(['/public/awards/dashboard']);
  }
}