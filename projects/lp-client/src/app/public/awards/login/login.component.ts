import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AwardsLoginComponent as MobileLoginComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-login',
  standalone: true,
  imports: [CommonModule, MobileLoginComponent],
  template: `
    <app-awards-login
      (loginSuccess)="onLoginSuccess($event)"
      (navigateToRegister)="onNavigateToRegister()">
    </app-awards-login>
  `,
  styles: []
})
export class AwardsLoginComponent {
  constructor(private router: Router) {}

  onLoginSuccess(user: any) {
    // Store user data in service or localStorage
    localStorage.setItem('awardsUserId', 'USER_DEFAULT');
    this.router.navigate(['/public/awards/dashboard']);
  }

  onNavigateToRegister() {
    this.router.navigate(['/public/awards/otp-request']);
  }
}