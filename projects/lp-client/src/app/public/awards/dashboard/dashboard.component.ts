import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { DashboardComponent as MobileDashboardComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-dashboard',
  standalone: true,
  imports: [CommonModule, MobileDashboardComponent],
  template: `
    <app-awards-dashboard
      [userId]="userId"
      (selectPartner)="onSelectPartner($event)"
      (navigate)="onNavigate($event)">
    </app-awards-dashboard>
  `,
  styles: []
})
export class AwardsDashboardComponent {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';

  constructor(private router: Router) {}

  onSelectPartner(partner: { id: string; name: string }) {
    this.router.navigate(['/public/awards/balance-detail'], {
      queryParams: { partnerId: partner.id }
    });
  }

  onNavigate(route: string) {
    switch(route) {
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card']);
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}