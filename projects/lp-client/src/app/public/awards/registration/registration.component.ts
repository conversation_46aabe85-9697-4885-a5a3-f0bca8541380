import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { RegistrationFormComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-registration',
  standalone: true,
  imports: [CommonModule, RegistrationFormComponent],
  template: `
    <app-registration-form
      [mobile]="mobile"
      [otp]="otp"
      (registrationComplete)="onRegistrationComplete($event)"
      (navigateBack)="onNavigateBack()">
    </app-registration-form>
  `,
  styles: []
})
export class AwardsRegistrationComponent implements OnInit {
  mobile = '';
  otp = '';

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.mobile = params['mobile'] || '';
      this.otp = params['otp'] || '';
    });
  }

  onRegistrationComplete(userId: string) {
    // Could store userId in a service or localStorage
    this.router.navigate(['/public/awards/login']);
  }

  onNavigateBack() {
    this.router.navigate(['/public/awards/otp-verification'], {
      queryParams: { mobile: this.mobile }
    });
  }
}