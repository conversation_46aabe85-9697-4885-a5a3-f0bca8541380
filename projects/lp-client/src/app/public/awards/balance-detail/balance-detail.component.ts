import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { BalanceDetailComponent as MobileBalanceDetailComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-balance-detail',
  standalone: true,
  imports: [CommonModule, MobileBalanceDetailComponent],
  template: `
    <app-balance-detail
      [userId]="userId"
      [partnerId]="partnerId"
      (navigate)="onNavigate($event)">
    </app-balance-detail>
  `,
  styles: []
})
export class AwardsBalanceDetailComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || 'pna';
    });
  }

  onNavigate(route: string) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card'], {
          queryParams: { partnerId: this.partnerId }
        });
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}