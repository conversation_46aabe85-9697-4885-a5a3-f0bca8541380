import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { TermsAcceptanceComponent as MobileTermsAcceptanceComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-terms-acceptance',
  standalone: true,
  imports: [CommonModule, MobileTermsAcceptanceComponent],
  template: `
    <app-terms-acceptance
      [programInfo]="programInfo"
      (termsAccepted)="onAcceptTerms()"
      (backClicked)="onCancel()"
      (navigationClicked)="onNavigate($event)">
    </app-terms-acceptance>
  `,
  styles: []
})
export class AwardsTermsAcceptanceComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';

  programInfo = {
    id: 'leroy<PERSON><PERSON>',
    name: '<PERSON>',
    logo: '<PERSON><PERSON>OY MERLIN',
    backgroundColor: '#66bb6a'
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || '';
      // Update programInfo based on partnerId
      this.programInfo = {
        ...this.programInfo,
        id: this.partnerId,
        name: this.getPartnerName(this.partnerId)
      };
    });
  }

  private getPartnerName(partnerId: string): string {
    const partnerNames: { [key: string]: string } = {
      'pna': 'Pick n Pay',
      'buildit': 'Build It',
      'leroymerlin': 'Leroy Merlin',
      'default': 'Partner'
    };
    return partnerNames[partnerId] || partnerNames['default'];
  }

  onAcceptTerms() {
    this.router.navigate(['/public/awards/enrollment-confirmation'], {
      queryParams: { partnerId: this.partnerId }
    });
  }

  onCancel() {
    this.router.navigate(['/public/awards/program-selection']);
  }

  onNavigate(route: any) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card']);
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}