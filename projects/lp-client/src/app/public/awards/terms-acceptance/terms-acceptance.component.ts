import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { TermsAcceptanceComponent as MobileTermsAcceptanceComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-terms-acceptance',
  standalone: true,
  imports: [CommonModule, MobileTermsAcceptanceComponent],
  template: `
    <app-terms-acceptance
      [userId]="userId"
      [partnerId]="partnerId"
      (acceptTerms)="onAcceptTerms()"
      (cancel)="onCancel()">
    </app-terms-acceptance>
  `,
  styles: []
})
export class AwardsTermsAcceptanceComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || '';
    });
  }

  onAcceptTerms() {
    this.router.navigate(['/public/awards/enrollment-confirmation'], {
      queryParams: { partnerId: this.partnerId }
    });
  }

  onCancel() {
    this.router.navigate(['/public/awards/program-selection']);
  }
}