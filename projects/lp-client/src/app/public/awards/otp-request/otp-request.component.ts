import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-awards-otp-request',
  standalone: true,
  imports: [],
  template: `
    <app-otp-request 
      (otpSent)="onOtpSent($event)"
      (navigateBack)="onNavigateBack()">
    </app-otp-request>
  `,
  styles: []
})
export class AwardsOtpRequestComponent {
  constructor(private router: Router) {}

  onOtpSent(mobile: string) {
    this.router.navigate(['/public/awards/otp-verification'], {
      queryParams: { mobile }
    });
  }

  onNavigateBack() {
    this.router.navigate(['/public/landing']);
  }
}