import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { OtpRequestComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-otp-request',
  standalone: true,
  imports: [CommonModule, OtpRequestComponent],
  template: `
    <app-otp-request 
      (otpSent)="onOtpSent($event)"
      (navigateBack)="onNavigateBack()">
    </app-otp-request>
  `,
  styles: []
})
export class AwardsOtpRequestComponent {
  constructor(private router: Router) {}

  onOtpSent(mobile: any) {
    this.router.navigate(['/public/awards/otp-verification'], {
      queryParams: { mobile }
    });
  }

  onNavigateBack() {
    this.router.navigate(['/public/landing']);
  }
}