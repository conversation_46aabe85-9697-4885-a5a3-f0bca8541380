<nav [ngClass]="containerClasses()" role="navigation" [attr.aria-label]="title">
  <div [ngClass]="contentClasses()">

    <!-- Left section: Back button, Logo, Title -->
    <div class="flex items-center space-x-3">
      <!-- Back Button -->
      <button
        *ngIf="showBackButton"
        type="button"
        (click)="onBackClick()"
        [ngClass]="navItemClasses()"
        [attr.aria-label]="backButtonLabel"
        class="flex-shrink-0">
        <span class="material-icons" [ngClass]="getIconClasses()">arrow_back</span>
        <span class="hidden sm:inline ml-1">{{ backButtonLabel }}</span>
      </button>

      <!-- Logo -->
      <button
        *ngIf="showLogo && logo"
        type="button"
        (click)="onLogoClick()"
        class="flex-shrink-0 focus:outline-none focus:ring-2 focus:ring-offset-2"
        aria-label="Logo">
        <img [src]="logo" [alt]="title + ' logo'" [ngClass]="getIconClasses('lg')" class="object-contain">
      </button>

      <!-- Title -->
      <button
        *ngIf="showTitle"
        type="button"
        (click)="onTitleClick()"
        class="font-semibold text-left focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-sm"
        [attr.aria-label]="'Navigate to ' + title">
        {{ title }}
      </button>
    </div>

    <!-- Center section: Navigation Items (Desktop) -->
    <div class="hidden md:flex items-center space-x-1" *ngIf="!isCollapsed() || !responsive">
      <ng-container *ngFor="let item of navigationItems; trackBy: trackByNavItem">
        <button
          type="button"
          (click)="onNavigationClick(item)"
          [ngClass]="navItemClasses()"
          [class.bg-black/20]="isItemActive(item)"
          [class.font-semibold]="isItemActive(item)"
          [disabled]="item.disabled"
          [attr.aria-current]="isItemActive(item) ? 'page' : null"
          [attr.aria-label]="getNavItemAriaLabel(item)">

          <span *ngIf="item.icon" class="material-icons" [ngClass]="getIconClasses()">{{ item.icon }}</span>
          <span>{{ item.label }}</span>

          <span
            *ngIf="item.badge"
            class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full ml-1"
            [attr.aria-label]="getBadgeAriaLabel(item.badge)">
            {{ item.badge }}
          </span>
        </button>
      </ng-container>
    </div>

    <!-- Right section: Actions -->
    <div class="flex items-center space-x-1">
      <!-- Action Buttons -->
      <ng-container *ngFor="let action of actions; trackBy: trackByAction">
        <button
          type="button"
          (click)="onActionClick(action)"
          [ngClass]="navItemClasses()"
          [attr.aria-label]="action.label"
          class="flex-shrink-0">
          <span class="material-icons" [ngClass]="getIconClasses()">{{ action.icon }}</span>
          <span class="hidden lg:inline ml-1">{{ action.label }}</span>
        </button>
      </ng-container>

      <!-- Mobile menu toggle -->
      <button
        *ngIf="responsive && navigationItems.length > 0"
        type="button"
        (click)="toggleCollapse()"
        [ngClass]="navItemClasses()"
        class="md:hidden flex-shrink-0"
        [attr.aria-expanded]="!isCollapsed()"
        aria-label="Toggle navigation menu">
        <span class="material-icons" [ngClass]="getIconClasses()">
          {{ isCollapsed() ? 'menu' : 'close' }}
        </span>
      </button>
    </div>
  </div>

  <!-- Mobile Navigation Menu (Collapsible) -->
  <div
    *ngIf="responsive && !isCollapsed() && mobileLayout !== 'hide'"
    class="md:hidden border-t"
    [class.border-gray-200]="variant === 'default'"
    [class.border-white/20]="variant !== 'default'">

    <div class="px-4 py-3 space-y-2" *ngIf="mobileLayout === 'stack'">
      <ng-container *ngFor="let item of navigationItems; trackBy: trackByNavItem">
        <button
          type="button"
          (click)="onNavigationClick(item)"
          class="w-full flex items-center justify-between px-3 py-2 rounded-md transition-colors duration-200 hover:bg-black/10"
          [class.bg-black/20]="isItemActive(item)"
          [class.font-semibold]="isItemActive(item)"
          [disabled]="item.disabled"
          [attr.aria-current]="isItemActive(item) ? 'page' : null">

          <div class="flex items-center space-x-3">
            <span *ngIf="item.icon" class="material-icons" [ngClass]="getIconClasses()">{{ item.icon }}</span>
            <span>{{ item.label }}</span>
          </div>

          <span
            *ngIf="item.badge"
            class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
            {{ item.badge }}
          </span>
        </button>
      </ng-container>
    </div>

    <!-- Collapsed horizontal layout -->
    <div class="px-4 py-3 flex space-x-2 overflow-x-auto" *ngIf="mobileLayout === 'collapse'">
      <ng-container *ngFor="let item of navigationItems; trackBy: trackByNavItem">
        <button
          type="button"
          (click)="onNavigationClick(item)"
          class="flex-shrink-0 flex flex-col items-center space-y-1 px-3 py-2 rounded-md transition-colors duration-200 hover:bg-black/10"
          [class.bg-black/20]="isItemActive(item)"
          [class.font-semibold]="isItemActive(item)"
          [disabled]="item.disabled"
          [attr.aria-current]="isItemActive(item) ? 'page' : null">

          <div class="relative">
            <span *ngIf="item.icon" class="material-icons" [ngClass]="getIconClasses()">{{ item.icon }}</span>
            <span
              *ngIf="item.badge"
              class="absolute -top-2 -right-2 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
              {{ item.badge }}
            </span>
          </div>
          <span class="text-xs">{{ item.label }}</span>
        </button>
      </ng-container>
    </div>
  </div>
</nav>