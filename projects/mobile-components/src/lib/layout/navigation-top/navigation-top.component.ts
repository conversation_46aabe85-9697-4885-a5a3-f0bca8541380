import { Component, Input, Output, EventEmitter, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';

// Interface for navigation item
export interface NavItem {
  id: string;
  label: string;
  icon?: string;
  url?: string;
  active?: boolean;
  badge?: string | number;
  disabled?: boolean;
}

// Interface for navigation actions
export interface NavAction {
  id: string;
  label: string;
  icon: string;
  handler?: () => void;
}

@Component({
  selector: 'lib-navigation-top',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './navigation-top.component.html',
  styleUrl: './navigation-top.component.css'
})
export class NavigationTopComponent {
  // Standard Tailwind inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() sticky: boolean = false;
  @Input() fixed: boolean = false;
  @Input() hidden: boolean = false;

  // Navigation-specific inputs with default data
  @Input() title: string = 'Navigation';
  @Input() showTitle: boolean = true;
  @Input() showBackButton: boolean = false;
  @Input() backButtonLabel: string = 'Back';
  @Input() logo: string = '';
  @Input() showLogo: boolean = false;
  
  @Input() navigationItems: NavItem[] = [
    { id: 'home', label: 'Home', icon: 'home', active: true },
    { id: 'dashboard', label: 'Dashboard', icon: 'dashboard' },
    { id: 'profile', label: 'Profile', icon: 'person', badge: '2' },
    { id: 'settings', label: 'Settings', icon: 'settings' }
  ];

  @Input() actions: NavAction[] = [
    { id: 'search', label: 'Search', icon: 'search' },
    { id: 'notifications', label: 'Notifications', icon: 'notifications' },
    { id: 'menu', label: 'Menu', icon: 'menu' }
  ];

  // Layout and behavior inputs
  @Input() backgroundColor: string = '';
  @Input() textColor: string = '';
  @Input() borderColor: string = '';
  @Input() shadow: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'sm';
  @Input() blur: boolean = false;
  @Input() transparent: boolean = false;
  @Input() centerContent: boolean = true;
  @Input() fullWidth: boolean = true;
  @Input() maxWidth: string = '';
  @Input() zIndex: string = 'z-50';

  // Responsive inputs
  @Input() responsive: boolean = true;
  @Input() mobileLayout: 'stack' | 'collapse' | 'hide' = 'collapse';
  @Input() showOnMobile: boolean = true;
  @Input() collapsedByDefault: boolean = false;

  // Event outputs
  @Output() navigationClick = new EventEmitter<NavItem>();
  @Output() actionClick = new EventEmitter<NavAction>();
  @Output() backClick = new EventEmitter<void>();
  @Output() logoClick = new EventEmitter<void>();
  @Output() titleClick = new EventEmitter<void>();

  // Component state
  isCollapsed = signal<boolean>(this.collapsedByDefault);

  // Computed styling properties
  containerClasses = computed(() => {
    const baseClasses = [
      'navigation-top w-full transition-all duration-300',
      this.sticky ? 'sticky top-0' : '',
      this.fixed ? 'fixed top-0 left-0 right-0' : '',
      this.hidden ? 'hidden' : 'block',
      this.zIndex
    ];

    // Size classes
    const sizeClasses = {
      xs: 'h-12 px-2',
      sm: 'h-14 px-3',
      md: 'h-16 px-4',
      lg: 'h-18 px-6',
      xl: 'h-20 px-8'
    };

    // Variant classes
    const variantClasses = {
      default: 'bg-white border-gray-200 text-gray-900',
      primary: 'bg-blue-600 border-blue-700 text-white',
      secondary: 'bg-gray-600 border-gray-700 text-white',
      success: 'bg-green-600 border-green-700 text-white',
      warning: 'bg-yellow-600 border-yellow-700 text-white',
      danger: 'bg-red-600 border-red-700 text-white'
    };

    // Rounded classes
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    // Shadow classes
    const shadowClasses = {
      none: 'shadow-none',
      sm: 'shadow-sm',
      md: 'shadow-md',
      lg: 'shadow-lg',
      xl: 'shadow-xl'
    };

    const classes = [
      ...baseClasses,
      sizeClasses[this.size],
      this.transparent ? 'bg-transparent' : variantClasses[this.variant],
      roundedClasses[this.rounded],
      shadowClasses[this.shadow],
      this.blur ? 'backdrop-blur-md' : '',
      this.backgroundColor || '',
      this.textColor || '',
      this.borderColor ? `border ${this.borderColor}` : 'border-b',
      this.className
    ];

    return classes.filter(Boolean).join(' ');
  });

  contentClasses = computed(() => {
    const classes = [
      'flex items-center justify-between h-full',
      this.fullWidth ? 'w-full' : '',
      this.maxWidth ? this.maxWidth : 'max-w-7xl',
      this.centerContent ? 'mx-auto' : ''
    ];

    return classes.filter(Boolean).join(' ');
  });

  navItemClasses = computed(() => {
    const sizeClasses = {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-5 py-2.5 text-lg',
      xl: 'px-6 py-3 text-xl'
    };

    return `inline-flex items-center space-x-2 rounded-md transition-colors duration-200 
            hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-offset-2 
            ${sizeClasses[this.size]}`;
  });

  // Navigation methods
  onNavigationClick(item: NavItem): void {
    if (item.disabled) return;
    this.navigationClick.emit(item);
  }

  onActionClick(action: NavAction): void {
    this.actionClick.emit(action);
    if (action.handler) {
      action.handler();
    }
  }

  onBackClick(): void {
    this.backClick.emit();
  }

  onLogoClick(): void {
    this.logoClick.emit();
  }

  onTitleClick(): void {
    this.titleClick.emit();
  }

  toggleCollapse(): void {
    this.isCollapsed.set(!this.isCollapsed());
  }

  // Utility methods
  isItemActive(item: NavItem): boolean {
    return item.active || false;
  }

  getIconClasses(size: string = this.size): string {
    const iconSizes = {
      xs: 'w-3 h-3',
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
      xl: 'w-7 h-7'
    };
    return iconSizes[size as keyof typeof iconSizes] || iconSizes.md;
  }

  trackByNavItem(index: number, item: NavItem): string {
    return item.id;
  }

  trackByAction(index: number, action: NavAction): string {
    return action.id;
  }

  // Helper methods for template
  getNavItemAriaLabel(item: NavItem): string {
    return item.label + (item.badge ? ' (' + item.badge + ' notifications)' : '');
  }

  getBadgeAriaLabel(badge: string | number): string {
    return badge + ' notifications';
  }
}
