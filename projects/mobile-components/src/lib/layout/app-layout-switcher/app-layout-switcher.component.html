<div [class]="containerClasses()">
  <!-- Header Section -->
  <div class="mb-6" *ngIf="showTitle || showSubtitle">
    <h3 *ngIf="showTitle" class="text-lg font-semibold mb-2">
      {{ title }}
    </h3>
    <p *ngIf="showSubtitle" class="text-sm text-gray-600">
      {{ subtitle }}
    </p>
  </div>

  <!-- Layout Options -->
  <div [class]="optionsContainerClasses()">
    <button
      *ngFor="let option of layoutOptions; trackBy: trackByOption"
      type="button"
      [class]="optionClasses() + ' ' + getOptionStateClasses(option)"
      [disabled]="option.disabled"
      [attr.aria-label]="option.name + (option.description ? ': ' + option.description : '')"
      [attr.aria-pressed]="isOptionSelected(option)"
      (click)="onOptionClick(option)"
      (keydown.enter)="onOptionClick(option)"
      (keydown.space)="onOptionClick(option)">
      
      <!-- Icon -->
      <span 
        *ngIf="showIcons && option.icon" 
        class="material-icons-outlined mb-2"
        [class]="getIconClasses()"
        aria-hidden="true">
        {{ option.icon }}
      </span>
      
      <!-- Option Name -->
      <span class="font-medium">{{ option.name }}</span>
      
      <!-- Description -->
      <span 
        *ngIf="showDescriptions && option.description" 
        class="text-xs opacity-75 mt-1">
        {{ option.description }}
      </span>
      
      <!-- Preview Placeholder -->
      <div 
        *ngIf="showPreviews && option.preview"
        class="mt-2 w-full h-8 bg-gray-100 rounded border"
        [style.background-image]="'url(' + option.preview + ')'"
        [style.background-size]="'cover'"
        [style.background-position]="'center'"
        aria-hidden="true">
      </div>
      
      <!-- Active Indicator -->
      <div 
        *ngIf="isOptionSelected(option)"
        class="absolute top-1 right-1 w-3 h-3 bg-current rounded-full opacity-75"
        aria-hidden="true">
      </div>
    </button>
  </div>

  <!-- Multi-select Info -->
  <div *ngIf="allowMultiSelect && selectedOptions().length > 0" class="mt-4 text-sm text-gray-600">
    <span class="font-medium">Selected:</span>
    <span class="ml-1">
      {{ getSelectedOptionsNames() }}
    </span>
  </div>
</div>
