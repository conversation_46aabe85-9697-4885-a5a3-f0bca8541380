import { Component, Input, Output, EventEmitter, computed, signal, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

// Interface for layout option
export interface LayoutOption {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  preview?: string;
  active?: boolean;
  disabled?: boolean;
}

@Component({
  selector: 'lib-app-layout-switcher',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './app-layout-switcher.component.html',
  styleUrl: './app-layout-switcher.component.css'
})
export class AppLayoutSwitcherComponent implements OnInit {
  // Standard Tailwind inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() sticky: boolean = false;
  @Input() fixed: boolean = false;
  @Input() hidden: boolean = false;

  // Layout switcher specific inputs with default data
  @Input() title: string = 'Layout Switcher';
  @Input() showTitle: boolean = true;
  @Input() subtitle: string = 'Choose your preferred layout';
  @Input() showSubtitle: boolean = true;
  
  @Input() layoutOptions: LayoutOption[] = [
    { 
      id: 'grid', 
      name: 'Grid Layout', 
      description: 'Organized grid view',
      icon: 'grid_view',
      active: true 
    },
    { 
      id: 'list', 
      name: 'List Layout', 
      description: 'Simple list view',
      icon: 'list' 
    },
    { 
      id: 'card', 
      name: 'Card Layout', 
      description: 'Card-based layout',
      icon: 'dashboard' 
    },
    { 
      id: 'compact', 
      name: 'Compact Layout', 
      description: 'Space-efficient view',
      icon: 'view_compact' 
    }
  ];

  // Layout and behavior inputs
  @Input() allowMultiSelect: boolean = false;
  @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';
  @Input() showIcons: boolean = true;
  @Input() showDescriptions: boolean = true;
  @Input() showPreviews: boolean = false;
  @Input() animateTransitions: boolean = true;
  @Input() backgroundColor: string = '';
  @Input() textColor: string = '';
  @Input() borderColor: string = '';
  @Input() shadow: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'sm';
  @Input() fullWidth: boolean = true;
  @Input() centerContent: boolean = true;
  @Input() gap: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'md';

  // Responsive inputs
  @Input() responsive: boolean = true;
  @Input() mobileOrientation: 'horizontal' | 'vertical' = 'vertical';
  @Input() stackOnMobile: boolean = true;

  // Event outputs
  @Output() layoutChange = new EventEmitter<LayoutOption>();
  @Output() multiSelectChange = new EventEmitter<LayoutOption[]>();
  @Output() optionClick = new EventEmitter<LayoutOption>();

  // Component state
  selectedOptions = signal<LayoutOption[]>([]);

  // Computed styling properties
  containerClasses = computed(() => {
    const baseClasses = [
      'layout-switcher transition-all duration-300',
      this.sticky ? 'sticky top-0' : '',
      this.fixed ? 'fixed top-0 left-0 right-0' : '',
      this.hidden ? 'hidden' : 'block',
      this.fullWidth ? 'w-full' : 'w-auto'
    ];

    // Size classes
    const sizeClasses = {
      xs: 'p-2 text-xs',
      sm: 'p-3 text-sm',
      md: 'p-4 text-base',
      lg: 'p-6 text-lg',
      xl: 'p-8 text-xl'
    };

    // Variant classes
    const variantClasses = {
      default: 'bg-white border-gray-200 text-gray-900',
      primary: 'bg-blue-50 border-blue-200 text-blue-900',
      secondary: 'bg-gray-50 border-gray-200 text-gray-900',
      success: 'bg-green-50 border-green-200 text-green-900',
      warning: 'bg-yellow-50 border-yellow-200 text-yellow-900',
      danger: 'bg-red-50 border-red-200 text-red-900'
    };

    // Rounded classes
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    // Shadow classes
    const shadowClasses = {
      none: 'shadow-none',
      sm: 'shadow-sm',
      md: 'shadow-md',
      lg: 'shadow-lg',
      xl: 'shadow-xl'
    };

    const classes = [
      ...baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      shadowClasses[this.shadow],
      this.backgroundColor || '',
      this.textColor || '',
      this.borderColor ? `border ${this.borderColor}` : 'border',
      this.centerContent ? 'mx-auto' : '',
      this.className
    ];

    return classes.filter(Boolean).join(' ');
  });

  optionsContainerClasses = computed(() => {
    const gapClasses = {
      none: 'gap-0',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8'
    };

    const orientationClasses = this.responsive && this.stackOnMobile 
      ? `flex flex-col sm:${this.orientation === 'horizontal' ? 'flex-row' : 'flex-col'}`
      : `flex ${this.orientation === 'horizontal' ? 'flex-row' : 'flex-col'}`;

    const classes = [
      orientationClasses,
      'items-center justify-center',
      gapClasses[this.gap],
      this.animateTransitions ? 'transition-all duration-300' : ''
    ];

    return classes.filter(Boolean).join(' ');
  });

  optionClasses = computed(() => {
    const sizeClasses = {
      xs: 'px-2 py-1 text-xs min-w-16',
      sm: 'px-3 py-2 text-sm min-w-20',
      md: 'px-4 py-3 text-base min-w-24',
      lg: 'px-6 py-4 text-lg min-w-28',
      xl: 'px-8 py-5 text-xl min-w-32'
    };

    return `flex flex-col items-center justify-center cursor-pointer border-2 
            rounded-lg transition-all duration-200 hover:shadow-md 
            focus:outline-none focus:ring-2 focus:ring-offset-2 
            ${sizeClasses[this.size]}`;
  });

  // Layout methods
  onOptionClick(option: LayoutOption): void {
    if (option.disabled) return;

    this.optionClick.emit(option);

    if (this.allowMultiSelect) {
      const currentSelected = this.selectedOptions();
      const isSelected = currentSelected.some(selected => selected.id === option.id);
      
      if (isSelected) {
        const newSelected = currentSelected.filter(selected => selected.id !== option.id);
        this.selectedOptions.set(newSelected);
      } else {
        this.selectedOptions.set([...currentSelected, option]);
      }
      
      this.multiSelectChange.emit(this.selectedOptions());
    } else {
      // Single select - update active state
      this.layoutOptions = this.layoutOptions.map(opt => ({
        ...opt,
        active: opt.id === option.id
      }));
      
      this.selectedOptions.set([option]);
      this.layoutChange.emit(option);
    }
  }

  // Utility methods
  isOptionSelected(option: LayoutOption): boolean {
    if (this.allowMultiSelect) {
      return this.selectedOptions().some(selected => selected.id === option.id);
    }
    return option.active || false;
  }

  getOptionStateClasses(option: LayoutOption): string {
    const isSelected = this.isOptionSelected(option);
    const isDisabled = option.disabled;

    if (isDisabled) {
      return 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed';
    }

    if (isSelected) {
      const selectedClasses = {
        default: 'border-gray-600 bg-gray-100 text-gray-900',
        primary: 'border-blue-600 bg-blue-100 text-blue-900 ring-blue-500',
        secondary: 'border-gray-600 bg-gray-100 text-gray-900 ring-gray-500',
        success: 'border-green-600 bg-green-100 text-green-900 ring-green-500',
        warning: 'border-yellow-600 bg-yellow-100 text-yellow-900 ring-yellow-500',
        danger: 'border-red-600 bg-red-100 text-red-900 ring-red-500'
      };
      return selectedClasses[this.variant];
    }

    return 'border-gray-200 bg-white text-gray-700 hover:border-gray-300 hover:bg-gray-50';
  }

  getIconClasses(size: string = this.size): string {
    const iconSizes = {
      xs: 'w-4 h-4',
      sm: 'w-5 h-5',
      md: 'w-6 h-6',
      lg: 'w-8 h-8',
      xl: 'w-10 h-10'
    };
    return iconSizes[size as keyof typeof iconSizes] || iconSizes.md;
  }

  trackByOption(index: number, option: LayoutOption): string {
    return option.id;
  }

  // Helper method for template
  getSelectedOptionsNames(): string {
    return this.selectedOptions().map(opt => opt.name).join(', ');
  }

  ngOnInit(): void {
    // Initialize selected options based on active states
    const activeOptions = this.layoutOptions.filter(option => option.active);
    this.selectedOptions.set(activeOptions);
  }
}
