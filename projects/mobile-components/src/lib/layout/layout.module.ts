import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular'; // Keep IonicModule if layout components use it

// Import standalone layout components
import { AppLayoutSwitcherComponent } from './app-layout-switcher/app-layout-switcher.component';
import { HeadLogoComponent } from './head-logo/head-logo.component';
import { LayoutComponent } from './layout.component';
// TEMPORARILY DISABLED: import { NavigationTopComponent } from './navigation-top/navigation-top.component';

// List of standalone components to manage
const LAYOUT_COMPONENTS = [
  AppLayoutSwitcherComponent,
  HeadLogoComponent,
  LayoutComponent,
  // TEMPORARILY DISABLED: NavigationTopComponent
];

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    ...LAYOUT_COMPONENTS // Import standalone components
  ],
  exports: [
    ...LAYOUT_COMPONENTS // Export standalone components
  ]
})
export class LayoutModule { }
