import { Component, Input, Output, EventEmitter, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Game, GameEvent } from 'lp-client-api';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Enhanced interfaces for game events
interface GameStartEvent {
  gameId: string;
  timestamp: Date;
  config: GamesCardConfig;
}

interface GameEndEvent {
  gameId: string;
  timestamp: Date;
  score: number;
  level: number;
  duration: number;
  result: 'win' | 'lose' | 'quit';
}

interface GameScoreEvent {
  gameId: string;
  timestamp: Date;
  score: number;
  previousScore: number;
  level: number;
}

interface GamePauseEvent {
  gameId: string;
  timestamp: Date;
  currentState: 'paused' | 'resumed';
}

interface GameErrorEvent {
  gameId: string;
  timestamp: Date;
  error: string;
  context?: any;
}

interface GameLevelEvent {
  gameId: string;
  timestamp: Date;
  level: number;
  previousLevel: number;
}

interface GameAchievementEvent {
  gameId: string;
  timestamp: Date;
  achievement: string;
  points: number;
}

interface GamesCardConfig {
  id: number;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  frequencyAttempts: number;
  showRating: boolean;
  showPlayCount: boolean;
  cardStyle: 'default' | 'compact' | 'detailed';
  imageAspectRatio: '1:1' | '16:9' | '4:3';
}

@Component({
  selector: 'app-games-card',
  templateUrl: './games-card.component.html',
  styleUrls: ['./games-card.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule]
})
export class GamesCardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  private defaultConfig: GamesCardConfig = {
    id: 0,
    difficulty: 'MEDIUM',
    frequency: 'DAILY',
    frequencyAttempts: 3,
    showRating: true,
    showPlayCount: true,
    cardStyle: 'default',
    imageAspectRatio: '16:9'
  };

  // === STANDARD GAME INPUTS ===
  // Base styling and configuration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'accent' = 'default';
  @Input() theme: 'light' | 'dark' | 'auto' = 'auto';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Game mechanics
  @Input() difficulty: 'easy' | 'medium' | 'hard' | 'expert' = 'medium';
  @Input() gameLevel: number = 1;
  @Input() maxLevel: number = 10;
  @Input() timeLimit: number = 0; // 0 = no limit
  @Input() lives: number = 3;

  // Scoring and rewards
  @Input() score: number = 0;
  @Input() highScore: number = 0;
  @Input() targetScore: number = 0;
  @Input() rewardPoints: number = 10;

  // Game state
  @Input() gameState: 'idle' | 'playing' | 'paused' | 'completed' | 'failed' = 'idle';
  @Input() autoStart: boolean = false;
  @Input() autoReset: boolean = false;
  @Input() saveProgress: boolean = true;

  // Audio and effects
  @Input() soundEnabled: boolean = true;
  @Input() effectsEnabled: boolean = true;
  @Input() vibrationEnabled: boolean = true;

  // Performance and accessibility
  @Input() frameRate: number = 60;
  @Input() reducedMotion: boolean = false;
  @Input() keyboardControls: boolean = true;
  @Input() screenReaderSupport: boolean = true;

  // === GAME-SPECIFIC INPUTS ===
  @Input() gameTitle: string = '';
  @Input() gameDescription: string = '';
  @Input() gameImage: string = '';
  @Input() playCount: number = 0;
  @Input() rating: number = 0;
  @Input() isFavorite: boolean = false;
  @Input() showPlayButton: boolean = true;
  @Input() showShareButton: boolean = true;
  @Input() cardStyle: 'default' | 'compact' | 'detailed' = 'default';

  // === GAME EVENT OUTPUTS ===
  @Output() gameStart = new EventEmitter<GameStartEvent>();
  @Output() gameEnd = new EventEmitter<GameEndEvent>();
  @Output() gameScore = new EventEmitter<GameScoreEvent>();
  @Output() gamePause = new EventEmitter<GamePauseEvent>();
  @Output() gameError = new EventEmitter<GameErrorEvent>();

  // Game-specific events
  @Output() gameSelect = new EventEmitter<{gameId: string, gameTitle: string}>();
  @Output() favoriteToggle = new EventEmitter<{gameId: string, isFavorite: boolean}>();
  @Output() shareGame = new EventEmitter<{gameId: string, gameTitle: string}>();
  @Output() cardClick = new EventEmitter<{gameId: string, gameTitle: string, event: MouseEvent}>();

  // Legacy inputs for backward compatibility
  @Input() game: any; // Replace 'any' with a proper Game interface
  @Input() bgColor: string = 'orange';
  @Input() borderColor: string = 'yellow';
  @Input() textColor: string = 'green';
  @Input() buttonColor: string = 'red';
  @Input() gameId?: string;
  @Input() gameInstance?: any;
  @Input()
  set config(value: GamesCardConfig) {
    console.log('Config input set with value:', value);
    this._config = {
      ...this.defaultConfig,
      ...value
    };
    console.log('Merged config:', this._config);
  }
  get config(): GamesCardConfig {
    return this._config;
  }
  private _config: GamesCardConfig;

  @Output() gameEvent = new EventEmitter<GameEvent>();

  // Computed styling properties
  get containerClasses(): string {
    const baseClasses = 'relative overflow-hidden rounded-lg transition-all duration-200 hover:scale-105 cursor-pointer';
    const sizeClasses = {
      xs: 'w-24 h-32 text-xs',
      sm: 'w-32 h-40 text-sm', 
      md: 'w-48 h-64 text-base',
      lg: 'w-64 h-80 text-lg',
      xl: 'w-80 h-96 text-xl'
    };
    const variantClasses = {
      default: 'bg-white shadow-lg',
      primary: 'bg-blue-50 border-2 border-blue-200',
      secondary: 'bg-gray-50 border-2 border-gray-200', 
      accent: 'bg-purple-50 border-2 border-purple-200'
    };
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-lg',
      lg: 'rounded-xl',
      full: 'rounded-full'
    };
    
    return `${baseClasses} ${sizeClasses[this.size]} ${variantClasses[this.variant]} ${roundedClasses[this.rounded]} ${this.className}`.trim();
  }

  get effectiveGameTitle(): string {
    return this.gameTitle || this.game?.name || this.game?.title || 'Untitled Game';
  }

  get effectiveGameDescription(): string {
    return this.gameDescription || this.game?.description || 'No description available';
  }

  get effectiveGameImage(): string {
    return this.gameImage || this.game?.image || this.game?.thumbnail || '/assets/images/default-game.png';
  }

  get effectiveGameId(): string {
    return this.gameId || this.game?.id || this.game?.name || 'unknown-game';
  }

  get starRating(): number[] {
    return Array(5).fill(0).map((_, i) => i < Math.floor(this.rating) ? 1 : 0);
  }

  // Game state
  duration = 0;
  startTime = 0;

  constructor(private router: Router, private cdr: ChangeDetectorRef) {
    this._config = { ...this.defaultConfig };
  }

  ngOnInit(): void {
    console.log('Initializing Games Card component with config:', this.config);
    
    if (this.game?.gameConfig) {
      const gameConfig = this.game.gameConfig.find((config: any) =>
        config.hasOwnProperty('cardStyle') || config.hasOwnProperty('showRating')
      ) as unknown as {
        id: number;
        difficulty: string;
        frequency: string;
        frequencyAttempts: number;
        showRating: boolean;
        showPlayCount: boolean;
        cardStyle: string;
        imageAspectRatio: string;
      };

      if (gameConfig) {
        console.log('Found game config:', gameConfig);
        this.config = {
          ...this.config,
          id: gameConfig.id ?? this.config.id,
          difficulty: (gameConfig.difficulty as 'EASY' | 'MEDIUM' | 'HARD') ?? this.config.difficulty,
          frequency: (gameConfig.frequency as 'DAILY' | 'WEEKLY' | 'MONTHLY') ?? this.config.frequency,
          frequencyAttempts: gameConfig.frequencyAttempts ?? this.config.frequencyAttempts,
          showRating: gameConfig.showRating ?? this.config.showRating,
          showPlayCount: gameConfig.showPlayCount ?? this.config.showPlayCount,
          cardStyle: (gameConfig.cardStyle as 'default' | 'compact' | 'detailed') ?? this.config.cardStyle,
          imageAspectRatio: (gameConfig.imageAspectRatio as '1:1' | '16:9' | '4:3') ?? this.config.imageAspectRatio
        };
      }
    }

    console.log('Final config before card init:', this.config);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  navigateToGame(gameName: string) {
    this.router.navigate(['/public/games/dashboard'], {
      queryParams: { game: gameName },
    });
  }

  onCardClick(event: MouseEvent): void {
    this.cardClick.emit({
      gameId: this.effectiveGameId,
      gameTitle: this.effectiveGameTitle,
      event: event
    });

    // Emit game start event
    this.gameStart.emit({
      gameId: this.effectiveGameId,
      timestamp: new Date(),
      config: this.config
    });

    // Navigate to game if enabled
    if (this.game?.name) {
      this.navigateToGame(this.game.name);
    }
  }

  onPlayButtonClick(event: MouseEvent): void {
    event.stopPropagation();
    
    this.gameSelect.emit({
      gameId: this.effectiveGameId,
      gameTitle: this.effectiveGameTitle
    });

    // Emit game start event
    this.gameStart.emit({
      gameId: this.effectiveGameId,
      timestamp: new Date(),
      config: this.config
    });

    if (this.game?.name) {
      this.navigateToGame(this.game.name);
    }
  }

  onFavoriteToggle(event: MouseEvent): void {
    event.stopPropagation();
    
    this.isFavorite = !this.isFavorite;
    this.favoriteToggle.emit({
      gameId: this.effectiveGameId,
      isFavorite: this.isFavorite
    });

    if (this.vibrationEnabled && navigator.vibrate) {
      navigator.vibrate(50);
    }
  }

  onShareGame(event: MouseEvent): void {
    event.stopPropagation();
    
    this.shareGame.emit({
      gameId: this.effectiveGameId,
      gameTitle: this.effectiveGameTitle
    });

    // Simple share functionality
    if (navigator.share) {
      navigator.share({
        title: this.effectiveGameTitle,
        text: this.effectiveGameDescription,
        url: window.location.href
      }).catch(console.error);
    } else {
      // Fallback - copy to clipboard
      navigator.clipboard.writeText(window.location.href).catch(console.error);
    }
  }

  private saveGameProgress(): void {
    this.duration = Math.floor((Date.now() - this.startTime) / 1000);

    const gameEvent: GameEvent = {
      id: 0, // Will be set by backend
      score: this.score,
      level: this.gameLevel,
      duration: this.duration,
      state: 'in_progress',
      payload: JSON.stringify({
        gameTitle: this.effectiveGameTitle,
        playCount: this.playCount,
        rating: this.rating,
        isFavorite: this.isFavorite
      })
    };

    this.gameEvent.emit(gameEvent);
  }
}