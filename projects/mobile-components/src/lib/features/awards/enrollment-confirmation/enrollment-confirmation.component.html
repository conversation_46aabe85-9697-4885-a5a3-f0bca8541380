<div class="enrollment-confirmation-container">
  <div class="header">
    <img [src]="logoSrc" alt="Logo" class="logo">
  </div>
  
  <div class="content">
    <div class="program-display">
      <div class="program-card" [style.background-color]="enrollmentInfo.backgroundColor">
        <span class="program-logo">{{ enrollmentInfo.programLogo }}</span>
      </div>
    </div>
    
    <div class="confirmation-section">
      <p class="welcome-text">
        Welcome {{ enrollmentInfo.userName }}
      </p>
      <p class="enrollment-text">
        You have been enrolled<br>
        and your Member Number<br>
        is: <strong>{{ enrollmentInfo.memberNumber }}</strong>
      </p>
      
      <button 
        type="button" 
        class="btn-primary"
        (click)="onSwitch()">
        Switch
      </button>
    </div>
  </div>

  <nav class="bottom-nav">
    <button class="nav-item active" (click)="onNavigate('home')">
      <span class="icon">🏠</span>
    </button>
    <button class="nav-item" (click)="onNavigate('profile')">
      <span class="icon">👤</span>
    </button>
    <button class="nav-item" (click)="onNavigate('card')">
      <span class="icon">💳</span>
    </button>
    <button class="nav-item" (click)="onNavigate('shops')">
      <span class="icon">🏪</span>
    </button>
    <button class="nav-item" (click)="onNavigate('transactions')">
      <span class="icon">📋</span>
    </button>
  </nav>
</div>