.enrollment-confirmation-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;

  .header {
    display: flex;
    justify-content: center;
    padding: 40px 0 30px;

    .logo {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: #333;
      padding: 15px;
    }
  }

  .content {
    flex: 1;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .program-display {
      margin-bottom: 40px;

      .program-card {
        width: 200px;
        height: 100px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

        .program-logo {
          color: white;
          font-weight: 700;
          font-size: 20px;
          text-align: center;
          text-transform: uppercase;
          letter-spacing: 1px;
        }
      }
    }

    .confirmation-section {
      width: 100%;
      max-width: 400px;
      text-align: center;

      .welcome-text {
        font-size: 20px;
        margin-bottom: 20px;
        color: #fff;
      }

      .enrollment-text {
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 40px;
        color: #ccc;

        strong {
          color: #fff;
          font-weight: 600;
        }
      }

      .btn-primary {
        width: 100%;
        padding: 16px;
        background-color: #e53935;
        color: #ffffff;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background-color: #c62828;
        }
      }
    }
  }

  .bottom-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #2a2a2a;
    border-top: 1px solid #3a3a3a;
    padding: 8px 0;
    position: sticky;
    bottom: 0;

    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      color: #666;
      cursor: pointer;
      padding: 8px;
      transition: all 0.3s ease;

      .icon {
        font-size: 20px;
        margin-bottom: 2px;
      }

      &:hover {
        color: #888;
      }

      &.active {
        color: #ffc107;
      }
    }
  }
}