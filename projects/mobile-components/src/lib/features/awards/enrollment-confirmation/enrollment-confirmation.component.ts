import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

export interface EnrollmentInfo {
  programId: string;
  programName: string;
  programLogo: string;
  backgroundColor: string;
  userName: string;
  memberNumber: string;
}

@Component({
  selector: 'app-enrollment-confirmation',
  templateUrl: './enrollment-confirmation.component.html',
  styleUrls: ['./enrollment-confirmation.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule]
})
export class EnrollmentConfirmationComponent implements OnInit {
  @Input() logoSrc = 'assets/images/logo.png';
  @Input() enrollmentInfo: EnrollmentInfo = {
    programId: 'leroymerlin',
    programName: '<PERSON> Merlin',
    programLogo: 'LEROY MERLIN',
    backgroundColor: '#66bb6a',
    userName: 'Marius',
    memberNumber: '135638765'
  };
  @Output() switchProgram = new EventEmitter<void>();
  @Output() navigationClicked = new EventEmitter<string>();

  ngOnInit(): void {}

  onSwitch(): void {
    this.switchProgram.emit();
  }

  onNavigate(route: string): void {
    this.navigationClicked.emit(route);
  }
}