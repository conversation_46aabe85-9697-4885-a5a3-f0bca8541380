import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

export interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  type: 'credit' | 'debit';
  currency: string;
}

export interface BalanceInfo {
  name: string;
  balance: number;
  currency: string;
  transactions: Transaction[];
}

@Component({
  selector: 'app-awards-balance-detail',
  templateUrl: './balance-detail.component.html',
  styleUrls: ['./balance-detail.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule]
})
export class BalanceDetailComponent implements OnInit {
  @Input() partnerLogo = 'PNA';
  @Input() partnerColor = '#e53935';
  @Input() balanceInfo: BalanceInfo = {
    name: 'Marius',
    balance: 50336,
    currency: 'R',
    transactions: []
  };
  @Output() navigationClicked = new EventEmitter<string>();
  @Output() backClicked = new EventEmitter<void>();

  ngOnInit(): void {
    // Initialize with mock transactions if none provided
    if (this.balanceInfo.transactions.length === 0) {
      this.balanceInfo.transactions = [
        {
          id: '1',
          date: '05/01/2025',
          description: 'Purchase',
          amount: -2500.00,
          type: 'debit',
          currency: 'R'
        },
        {
          id: '2',
          date: '10/01/2025',
          description: 'Purchase',
          amount: -1200.00,
          type: 'debit',
          currency: 'R'
        },
        {
          id: '3',
          date: '20/01/2025',
          description: 'Purchase',
          amount: -3100.00,
          type: 'debit',
          currency: 'R'
        },
        {
          id: '4',
          date: '31/01/2025',
          description: '',
          amount: 6109.00,
          type: 'credit',
          currency: 'R'
        }
      ];
    }
  }

  onNavigate(route: string): void {
    this.navigationClicked.emit(route);
  }

  onBack(): void {
    this.backClicked.emit();
  }

  formatBalance(amount: number): string {
    return amount.toLocaleString('en-ZA');
  }

  formatAmount(amount: number, type: 'credit' | 'debit'): string {
    const formatted = Math.abs(amount).toFixed(2);
    return type === 'credit' ? formatted : formatted;
  }
}