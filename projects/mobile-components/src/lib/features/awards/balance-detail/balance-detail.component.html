<div class="balance-detail-container">
  <div class="header" [style.background-color]="partnerColor">
    <div class="partner-logo">{{ partnerLogo }}</div>
  </div>
  
  <div class="content">
    <div class="balance-info">
      <h1 class="greeting">Hello {{ balanceInfo.name }}</h1>
      <div class="balance">
        <span class="label">Balance: </span>
        <span class="amount">{{ formatBalance(balanceInfo.balance) }}</span>
        <span class="fireworks">🎆</span>
      </div>
    </div>

    <div class="history-section">
      <h2 class="section-title">History</h2>
      
      <div class="transactions-list">
        <div *ngFor="let transaction of balanceInfo.transactions" class="transaction-item">
          <div class="transaction-info">
            <span class="date">{{ transaction.date }}</span>
            <span class="description">{{ transaction.description }}</span>
          </div>
          <div class="transaction-amount" [class.credit]="transaction.type === 'credit'">
            <span class="currency">{{ transaction.currency }}</span>
            <span class="amount">{{ formatAmount(transaction.amount, transaction.type) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <nav class="bottom-nav">
    <button class="nav-item active" (click)="onNavigate('home')">
      <span class="icon">🏠</span>
    </button>
    <button class="nav-item" (click)="onNavigate('profile')">
      <span class="icon">👤</span>
    </button>
    <button class="nav-item" (click)="onNavigate('card')">
      <span class="icon">💳</span>
    </button>
    <button class="nav-item" (click)="onNavigate('shops')">
      <span class="icon">🏪</span>
    </button>
    <button class="nav-item" (click)="onNavigate('transactions')">
      <span class="icon">📋</span>
    </button>
  </nav>
</div>