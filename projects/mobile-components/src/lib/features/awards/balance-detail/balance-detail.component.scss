.balance-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;

  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
    background-color: #e53935;

    .partner-logo {
      color: white;
      font-weight: 700;
      font-size: 32px;
      text-transform: uppercase;
      letter-spacing: 2px;
    }
  }

  .content {
    flex: 1;
    padding: 20px;
    
    .balance-info {
      text-align: center;
      margin-bottom: 30px;

      .greeting {
        font-size: 24px;
        font-weight: 300;
        margin: 0 0 10px;
      }

      .balance {
        font-size: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        .label {
          color: #aaa;
        }

        .amount {
          font-weight: 600;
        }

        .fireworks {
          font-size: 24px;
          animation: pulse 1s ease-in-out infinite;
        }
      }
    }

    .history-section {
      .section-title {
        font-size: 18px;
        font-weight: 500;
        margin: 0 0 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #3a3a3a;
      }

      .transactions-list {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .transaction-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px;
          background-color: #2a2a2a;
          border-radius: 8px;

          .transaction-info {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .date {
              font-size: 12px;
              color: #888;
            }

            .description {
              font-size: 14px;
              color: #ccc;
            }
          }

          .transaction-amount {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #f44336;

            &.credit {
              color: #4caf50;
            }

            .currency {
              font-size: 14px;
            }

            .amount {
              font-size: 16px;
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  .bottom-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #2a2a2a;
    border-top: 1px solid #3a3a3a;
    padding: 8px 0;
    position: sticky;
    bottom: 0;

    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      color: #666;
      cursor: pointer;
      padding: 8px;
      transition: all 0.3s ease;

      .icon {
        font-size: 20px;
        margin-bottom: 2px;
      }

      &:hover {
        color: #888;
      }

      &.active {
        color: #e53935;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}