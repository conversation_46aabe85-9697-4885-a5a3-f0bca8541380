import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

export interface ProgramInfo {
  id: string;
  name: string;
  logo: string;
  backgroundColor: string;
}

@Component({
  selector: 'app-terms-acceptance',
  templateUrl: './terms-acceptance.component.html',
  styleUrls: ['./terms-acceptance.component.scss']
})
export class TermsAcceptanceComponent implements OnInit {
  @Input() logoSrc = 'assets/images/logo.png';
  @Input() programInfo: ProgramInfo = {
    id: 'leroymerlin',
    name: '<PERSON>',
    logo: 'LEROY MERLIN',
    backgroundColor: '#66bb6a'
  };
  @Output() termsAccepted = new EventEmitter<void>();
  @Output() backClicked = new EventEmitter<void>();
  @Output() navigationClicked = new EventEmitter<string>();

  termsForm: FormGroup;
  loading = false;

  constructor(private fb: FormBuilder) {
    this.termsForm = this.fb.group({
      acceptTerms: [false, Validators.requiredTrue]
    });
  }

  ngOnInit(): void {}

  onSubmit(): void {
    if (this.termsForm.valid) {
      this.loading = true;
      this.termsAccepted.emit();
    }
  }

  onBack(): void {
    this.backClicked.emit();
  }

  onNavigate(route: string): void {
    this.navigationClicked.emit(route);
  }

  get acceptTermsControl() {
    return this.termsForm.get('acceptTerms');
  }
}