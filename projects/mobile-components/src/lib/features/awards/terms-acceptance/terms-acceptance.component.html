<div class="terms-acceptance-container">
  <div class="header">
    <img [src]="logoSrc" alt="Logo" class="logo">
  </div>
  
  <div class="content">
    <div class="program-display">
      <div class="program-card" [style.background-color]="programInfo.backgroundColor">
        <span class="program-logo">{{ programInfo.logo }}</span>
      </div>
    </div>
    
    <div class="terms-section">
      <p class="terms-text">
        I accept the Terms and Conditions of the program
      </p>
      
      <form [formGroup]="termsForm" (ngSubmit)="onSubmit()">
        <label class="checkbox-container">
          <input 
            type="checkbox" 
            formControlName="acceptTerms"
            class="checkbox">
          <span class="checkmark"></span>
          <span class="label-text">I agree to the terms and conditions</span>
        </label>
        
        <button 
          type="submit" 
          class="btn-primary"
          [disabled]="termsForm.invalid || loading">
          {{ loading ? 'Processing...' : 'Submit' }}
        </button>
        
        <button 
          type="button" 
          class="btn-secondary"
          (click)="onBack()"
          [disabled]="loading">
          Back
        </button>
      </form>
    </div>
  </div>

  <nav class="bottom-nav">
    <button class="nav-item active" (click)="onNavigate('home')">
      <span class="icon">🏠</span>
    </button>
    <button class="nav-item" (click)="onNavigate('profile')">
      <span class="icon">👤</span>
    </button>
    <button class="nav-item" (click)="onNavigate('card')">
      <span class="icon">💳</span>
    </button>
    <button class="nav-item" (click)="onNavigate('shops')">
      <span class="icon">🏪</span>
    </button>
    <button class="nav-item" (click)="onNavigate('transactions')">
      <span class="icon">📋</span>
    </button>
  </nav>
</div>