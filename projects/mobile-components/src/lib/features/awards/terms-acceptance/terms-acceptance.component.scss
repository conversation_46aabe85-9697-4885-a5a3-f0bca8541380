.terms-acceptance-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;

  .header {
    display: flex;
    justify-content: center;
    padding: 40px 0 30px;

    .logo {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: #333;
      padding: 15px;
    }
  }

  .content {
    flex: 1;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .program-display {
      margin-bottom: 40px;

      .program-card {
        width: 200px;
        height: 100px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

        .program-logo {
          color: white;
          font-weight: 700;
          font-size: 20px;
          text-align: center;
          text-transform: uppercase;
          letter-spacing: 1px;
        }
      }
    }

    .terms-section {
      width: 100%;
      max-width: 400px;
      text-align: center;

      .terms-text {
        font-size: 16px;
        margin-bottom: 30px;
        color: #ccc;
      }

      form {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .checkbox-container {
          display: flex;
          align-items: center;
          position: relative;
          padding-left: 35px;
          margin-bottom: 20px;
          cursor: pointer;
          user-select: none;
          text-align: left;

          .checkbox {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;

            &:checked ~ .checkmark {
              background-color: #e53935;
              border-color: #e53935;

              &:after {
                display: block;
              }
            }
          }

          .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 24px;
            width: 24px;
            background-color: transparent;
            border: 2px solid #666;
            border-radius: 4px;
            transition: all 0.3s ease;

            &:after {
              content: "";
              position: absolute;
              display: none;
              left: 8px;
              top: 4px;
              width: 6px;
              height: 12px;
              border: solid white;
              border-width: 0 2px 2px 0;
              transform: rotate(45deg);
            }
          }

          .label-text {
            font-size: 14px;
            color: #ccc;
          }
        }

        .btn-primary {
          width: 100%;
          padding: 16px;
          background-color: #e53935;
          color: #ffffff;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            background-color: #c62828;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        .btn-secondary {
          width: 100%;
          padding: 16px;
          background-color: transparent;
          color: #888;
          border: 1px solid #3a3a3a;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            border-color: #666;
            color: #aaa;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }
  }

  .bottom-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #2a2a2a;
    border-top: 1px solid #3a3a3a;
    padding: 8px 0;
    position: sticky;
    bottom: 0;

    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      color: #666;
      cursor: pointer;
      padding: 8px;
      transition: all 0.3s ease;

      .icon {
        font-size: 20px;
        margin-bottom: 2px;
      }

      &:hover {
        color: #888;
      }

      &.active {
        color: #ffc107;
      }
    }
  }
}