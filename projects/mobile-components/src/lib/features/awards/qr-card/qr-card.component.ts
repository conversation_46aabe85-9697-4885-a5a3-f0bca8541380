import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';

export interface QRCardInfo {
  partnerId: string;
  partnerName: string;
  partnerLogo: string;
  backgroundColor: string;
  userName: string;
  userNumber: string;
  qrCodeData?: string;
}

@Component({
  selector: 'app-qr-card',
  templateUrl: './qr-card.component.html',
  styleUrls: ['./qr-card.component.scss']
})
export class QrCardComponent implements OnInit {
  @Input() cardInfo: QRCardInfo = {
    partnerId: 'buildit',
    partnerName: 'Build It',
    partnerLogo: 'Build IT',
    backgroundColor: '#c62828',
    userName: 'Marius <PERSON>',
    userNumber: '1237896114'
  };
  @Output() navigationClicked = new EventEmitter<string>();
  @Output() backClicked = new EventEmitter<void>();

  qrCodePlaceholder = '';

  ngOnInit(): void {
    // Generate QR code placeholder
    this.generateQRPlaceholder();
  }

  private generateQRPlaceholder(): void {
    // Create a simple ASCII QR code placeholder
    const pattern = [
      '█▀▀▀▀▀█ ▄▀▄ █▀▀▀▀▀█',
      '█ ███ █ ▀▄▀ █ ███ █',
      '█ ▀▀▀ █ ▄▀▄ █ ▀▀▀ █',
      '▀▀▀▀▀▀▀ █▄█ ▀▀▀▀▀▀▀',
      '█▀▄ ▄▀▀▄▀▄▀▄█▀█▄▀▄█',
      '▀▄█▄▀▀▀ ▄█▄ ▀▄█▄▀▀▄',
      '█▀▀▀▀▀█ ▄▀▄ █▄▀█▄▀█',
      '█ ███ █ ▀▄▀ ▄█▄▀▄█▄',
      '█ ▀▀▀ █ ▄▀▄ ▀▄█▄▀▄█',
      '▀▀▀▀▀▀▀ ▀▀▀ ▀▀▀▀▀▀▀'
    ];
    this.qrCodePlaceholder = pattern.join('\n');
  }

  onNavigate(route: string): void {
    this.navigationClicked.emit(route);
  }

  onBack(): void {
    this.backClicked.emit();
  }
}