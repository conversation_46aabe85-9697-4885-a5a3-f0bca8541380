.qr-card-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;

  .card-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;

    .card {
      width: 100%;
      max-width: 350px;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
      background-color: #c62828;

      .card-header {
        padding: 20px;
        text-align: center;
        background-color: rgba(0, 0, 0, 0.1);

        .partner-logo {
          color: white;
          font-weight: 700;
          font-size: 28px;
          text-transform: uppercase;
          letter-spacing: 2px;
        }
      }

      .card-body {
        padding: 20px;
        background-color: rgba(255, 255, 255, 0.95);
        color: #333;

        .user-info {
          text-align: center;
          margin-bottom: 20px;

          .user-name {
            font-size: 20px;
            font-weight: 600;
            margin: 0 0 8px;
            color: #1a1a1a;
          }

          .user-number {
            font-size: 16px;
            margin: 0;
            color: #666;
            letter-spacing: 1px;
          }
        }

        .qr-code-section {
          display: flex;
          justify-content: center;
          padding: 20px 0;

          .qr-code {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            .qr-placeholder {
              margin: 0;
              font-family: monospace;
              font-size: 12px;
              line-height: 1.2;
              color: #000;
              white-space: pre;
            }
          }
        }
      }
    }
  }

  .bottom-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #2a2a2a;
    border-top: 1px solid #3a3a3a;
    padding: 8px 0;
    position: sticky;
    bottom: 0;

    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      color: #666;
      cursor: pointer;
      padding: 8px;
      transition: all 0.3s ease;

      .icon {
        font-size: 20px;
        margin-bottom: 2px;
      }

      &:hover {
        color: #888;
      }

      &.active {
        color: #ffc107;
      }
    }
  }
}