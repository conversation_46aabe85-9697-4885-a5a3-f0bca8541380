<div class="qr-card-container">
  <div class="card-wrapper">
    <div class="card" [style.background-color]="cardInfo.backgroundColor">
      <div class="card-header">
        <div class="partner-logo">{{ cardInfo.partnerLogo }}</div>
      </div>
      
      <div class="card-body">
        <div class="user-info">
          <h2 class="user-name">{{ cardInfo.userName }}</h2>
          <p class="user-number">{{ cardInfo.userNumber }}</p>
        </div>
        
        <div class="qr-code-section">
          <div class="qr-code">
            <pre class="qr-placeholder">{{ qrCodePlaceholder }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>

  <nav class="bottom-nav">
    <button class="nav-item active" (click)="onNavigate('home')">
      <span class="icon">🏠</span>
    </button>
    <button class="nav-item" (click)="onNavigate('profile')">
      <span class="icon">👤</span>
    </button>
    <button class="nav-item" (click)="onNavigate('card')">
      <span class="icon">💳</span>
    </button>
    <button class="nav-item" (click)="onNavigate('shops')">
      <span class="icon">🏪</span>
    </button>
    <button class="nav-item" (click)="onNavigate('transactions')">
      <span class="icon">📋</span>
    </button>
  </nav>
</div>