<div class="dashboard-container">
  <div class="header">
    <img [src]="logoSrc" alt="Logo" class="logo">
  </div>
  
  <div class="content">
    <h1 class="greeting">Hello {{ user.name }}</h1>
    
    <div class="balance-section">
      <p class="balance-label">Check your Balance and Rewards.</p>
    </div>

    <div class="partners-grid">
      <button 
        *ngFor="let partner of partners" 
        class="partner-card"
        [style.background-color]="partner.backgroundColor"
        (click)="onPartnerClick(partner.id)">
        <span class="partner-logo">{{ partner.logo }}</span>
      </button>
    </div>
  </div>

  <nav class="bottom-nav">
    <button class="nav-item active" (click)="onNavigate('home')">
      <span class="icon">🏠</span>
    </button>
    <button class="nav-item" (click)="onNavigate('locations')">
      <span class="icon">🏪</span>
    </button>
    <button class="nav-item" (click)="onNavigate('transactions')">
      <span class="icon">📋</span>
    </button>
  </nav>
</div>