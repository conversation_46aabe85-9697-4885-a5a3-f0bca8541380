import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

export interface UserBalance {
  name: string;
  balance: number;
  currency: string;
}

export interface RewardPartner {
  id: string;
  name: string;
  logo: string;
  backgroundColor: string;
}

@Component({
  selector: 'app-awards-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule]
})
export class AwardsDashboardComponent implements OnInit {
  @Input() logoSrc = 'assets/images/logo.png';
  @Input() user: UserBalance = {
    name: 'Marius',
    balance: 0,
    currency: 'R'
  };
  @Output() navigationClicked = new EventEmitter<string>();
  @Output() partnerClicked = new EventEmitter<string>();
  @Output() balanceClicked = new EventEmitter<void>();

  partners: RewardPartner[] = [
    {
      id: 'pna',
      name: 'Pick n Pay',
      logo: 'PNA',
      backgroundColor: '#e53935'
    },
    {
      id: 'buildit',
      name: 'Build It',
      logo: 'Build IT',
      backgroundColor: '#c62828'
    },
    {
      id: 'leroymerlin',
      name: 'Leroy Merlin',
      logo: 'LEROY MERLIN',
      backgroundColor: '#66bb6a'
    }
  ];

  ngOnInit(): void {}

  onNavigate(route: string): void {
    this.navigationClicked.emit(route);
  }

  onPartnerClick(partnerId: string): void {
    this.partnerClicked.emit(partnerId);
  }

  onBalanceClick(): void {
    this.balanceClicked.emit();
  }
}