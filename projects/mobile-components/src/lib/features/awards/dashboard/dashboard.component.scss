.dashboard-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;

  .header {
    display: flex;
    justify-content: center;
    padding: 40px 0 30px;

    .logo {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: #333;
      padding: 15px;
    }
  }

  .content {
    flex: 1;
    padding: 0 20px;
    
    .greeting {
      font-size: 28px;
      font-weight: 300;
      margin: 0 0 20px;
      text-align: center;
    }

    .balance-section {
      margin-bottom: 40px;

      .balance-label {
        font-size: 16px;
        color: #aaa;
        text-align: center;
        margin: 0;
      }
    }

    .partners-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      max-width: 400px;
      margin: 0 auto;

      .partner-card {
        aspect-ratio: 1;
        border: none;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        min-height: 120px;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        &:nth-child(3) {
          grid-column: 1 / -1;
          aspect-ratio: 2;
        }

        .partner-logo {
          color: white;
          font-weight: 700;
          font-size: 18px;
          text-align: center;
          text-transform: uppercase;
          letter-spacing: 1px;
          padding: 10px;
        }
      }
    }
  }

  .bottom-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #2a2a2a;
    border-top: 1px solid #3a3a3a;
    padding: 8px 0;
    position: sticky;
    bottom: 0;

    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      color: #666;
      cursor: pointer;
      padding: 8px;
      transition: all 0.3s ease;

      .icon {
        font-size: 24px;
        margin-bottom: 2px;
      }

      &:hover {
        color: #888;
      }

      &.active {
        color: #e53935;
      }
    }
  }
}