import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-otp-verification',
  templateUrl: './otp-verification.component.html',
  styleUrls: ['./otp-verification.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, IonicModule]
})
export class OtpVerificationComponent implements OnInit {
  @Input() logoSrc = 'assets/images/logo.png';
  @Input() mobile = '';
  @Output() otpVerified = new EventEmitter<{ mobile: string; otp: string }>();
  @Output() backClicked = new EventEmitter<void>();
  
  otpForm: FormGroup;
  loading = false;

  constructor(private fb: FormBuilder) {
    this.otpForm = this.fb.group({
      otp: ['', [Validators.required, Validators.pattern(/^[0-9]{6}$/)]]
    });
  }

  ngOnInit(): void {
    if (!this.mobile) {
      console.warn('Mobile number not provided to OTP verification component');
    }
  }

  onContinue(): void {
    if (this.otpForm.valid) {
      this.loading = true;
      const otp = this.otpForm.get('otp')?.value;
      this.otpVerified.emit({ mobile: this.mobile, otp });
    }
  }

  onBack(): void {
    this.backClicked.emit();
  }

  get otpControl() {
    return this.otpForm.get('otp');
  }
}