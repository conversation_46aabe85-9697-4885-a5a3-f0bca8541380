<div class="otp-verification-container">
  <div class="header">
    <img [src]="logoSrc" alt="Logo" class="logo">
  </div>
  
  <div class="content">
    <h2 class="title">Register</h2>
    
    <form [formGroup]="otpForm" (ngSubmit)="onContinue()">
      <div class="form-group">
        <input 
          type="text" 
          formControlName="otp" 
          placeholder="Insert OTP"
          class="form-control"
          maxlength="6"
          [class.error]="otpControl?.invalid && otpControl?.touched">
        
        <div class="error-message" *ngIf="otpControl?.invalid && otpControl?.touched">
          <span *ngIf="otpControl?.errors?.['required']">OTP is required</span>
          <span *ngIf="otpControl?.errors?.['pattern']">Please enter a valid 6-digit OTP</span>
        </div>
      </div>
      
      <button 
        type="submit" 
        class="btn-primary"
        [disabled]="otpForm.invalid || loading">
        {{ loading ? 'Verifying...' : 'Continue' }}
      </button>
      
      <button 
        type="button" 
        class="btn-secondary"
        (click)="onBack()"
        [disabled]="loading">
        Back
      </button>
    </form>
  </div>
</div>