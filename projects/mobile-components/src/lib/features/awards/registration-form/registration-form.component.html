<div class="registration-form-container">
  <div class="header">
    <img [src]="logoSrc" alt="Logo" class="logo">
  </div>
  
  <div class="content">
    <h2 class="title">Register</h2>
    
    <form [formGroup]="registrationForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <input 
          type="text" 
          formControlName="name" 
          placeholder="Name"
          class="form-control"
          [class.error]="getControl('name')?.invalid && getControl('name')?.touched">
        
        <div class="error-message" *ngIf="getControl('name')?.invalid && getControl('name')?.touched">
          <span *ngIf="getControl('name')?.errors?.['required']">Name is required</span>
          <span *ngIf="getControl('name')?.errors?.['minlength']">Name must be at least 2 characters</span>
        </div>
      </div>

      <div class="form-group">
        <input 
          type="text" 
          formControlName="surname" 
          placeholder="Surname"
          class="form-control"
          [class.error]="getControl('surname')?.invalid && getControl('surname')?.touched">
        
        <div class="error-message" *ngIf="getControl('surname')?.invalid && getControl('surname')?.touched">
          <span *ngIf="getControl('surname')?.errors?.['required']">Surname is required</span>
          <span *ngIf="getControl('surname')?.errors?.['minlength']">Surname must be at least 2 characters</span>
        </div>
      </div>

      <div class="form-group">
        <input 
          type="email" 
          formControlName="email" 
          placeholder="Email"
          class="form-control"
          [class.error]="getControl('email')?.invalid && getControl('email')?.touched">
        
        <div class="error-message" *ngIf="getControl('email')?.invalid && getControl('email')?.touched">
          <span *ngIf="getControl('email')?.errors?.['required']">Email is required</span>
          <span *ngIf="getControl('email')?.errors?.['email']">Please enter a valid email address</span>
        </div>
      </div>

      <div class="form-group">
        <input 
          type="date" 
          formControlName="dateOfBirth" 
          placeholder="Date of Birth"
          class="form-control"
          [class.error]="getControl('dateOfBirth')?.invalid && getControl('dateOfBirth')?.touched">
        
        <div class="error-message" *ngIf="getControl('dateOfBirth')?.invalid && getControl('dateOfBirth')?.touched">
          <span *ngIf="getControl('dateOfBirth')?.errors?.['required']">Date of Birth is required</span>
        </div>
      </div>

      <div class="form-group">
        <input 
          type="text" 
          formControlName="idNumber" 
          placeholder="ID Number"
          class="form-control"
          maxlength="13"
          [class.error]="getControl('idNumber')?.invalid && getControl('idNumber')?.touched">
        
        <div class="error-message" *ngIf="getControl('idNumber')?.invalid && getControl('idNumber')?.touched">
          <span *ngIf="getControl('idNumber')?.errors?.['required']">ID Number is required</span>
          <span *ngIf="getControl('idNumber')?.errors?.['pattern']">Please enter a valid 13-digit ID number</span>
        </div>
      </div>
      
      <button 
        type="submit" 
        class="btn-primary"
        [disabled]="registrationForm.invalid || loading">
        {{ loading ? 'Submitting...' : 'Submit' }}
      </button>
      
      <button 
        type="button" 
        class="btn-secondary"
        (click)="onBack()"
        [disabled]="loading">
        Back
      </button>
    </form>
  </div>
</div>