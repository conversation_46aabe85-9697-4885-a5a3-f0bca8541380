import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

export interface RegistrationData {
  mobile: string;
  otp: string;
  name: string;
  surname: string;
  email: string;
  dateOfBirth: string;
  idNumber: string;
}

@Component({
  selector: 'app-registration-form',
  templateUrl: './registration-form.component.html',
  styleUrls: ['./registration-form.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, IonicModule]
})
export class RegistrationFormComponent {
  @Input() logoSrc = 'assets/images/logo.png';
  @Input() mobile = '';
  @Input() otp = '';
  @Output() registrationSubmitted = new EventEmitter<RegistrationData>();
  @Output() backClicked = new EventEmitter<void>();
  
  registrationForm: FormGroup;
  loading = false;

  constructor(private fb: FormBuilder) {
    this.registrationForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      surname: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      dateOfBirth: ['', [Validators.required]],
      idNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{13}$/)]]
    });
  }

  onSubmit(): void {
    if (this.registrationForm.valid) {
      this.loading = true;
      const formData = this.registrationForm.value;
      const registrationData: RegistrationData = {
        mobile: this.mobile,
        otp: this.otp,
        ...formData
      };
      this.registrationSubmitted.emit(registrationData);
    }
  }

  onBack(): void {
    this.backClicked.emit();
  }

  getControl(name: string) {
    return this.registrationForm.get(name);
  }
}