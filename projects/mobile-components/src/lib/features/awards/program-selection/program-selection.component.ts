import { Component, EventEmitter, Input, Output } from '@angular/core';

export interface Program {
  id: string;
  name: string;
  logo: string;
  backgroundColor: string;
  enrolled: boolean;
}

@Component({
  selector: 'app-program-selection',
  templateUrl: './program-selection.component.html',
  styleUrls: ['./program-selection.component.scss']
})
export class ProgramSelectionComponent {
  @Input() logoSrc = 'assets/images/logo.png';
  @Output() programSelected = new EventEmitter<string>();
  @Output() navigationClicked = new EventEmitter<string>();

  programs: Program[] = [
    {
      id: 'pna',
      name: 'Pick n Pay',
      logo: 'PNA',
      backgroundColor: '#e53935',
      enrolled: false
    },
    {
      id: 'buildit',
      name: 'Build It',
      logo: 'Build IT',
      backgroundColor: '#c62828',
      enrolled: false
    },
    {
      id: 'leroy<PERSON><PERSON>',
      name: '<PERSON>',
      logo: 'LEROY MERLIN',
      backgroundColor: '#66bb6a',
      enrolled: false
    }
  ];

  onProgramClick(programId: string): void {
    this.programSelected.emit(programId);
  }

  onNavigate(route: string): void {
    this.navigationClicked.emit(route);
  }
}