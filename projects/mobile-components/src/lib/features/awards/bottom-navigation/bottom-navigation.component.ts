import { Component, EventEmitter, Input, Output } from '@angular/core';

export interface NavigationItem {
  id: string;
  icon: string;
  label: string;
  active?: boolean;
}

@Component({
  selector: 'app-awards-bottom-navigation',
  templateUrl: './bottom-navigation.component.html',
  styleUrls: ['./bottom-navigation.component.scss']
})
export class BottomNavigationComponent {
  @Input() items: NavigationItem[] = [
    { id: 'home', icon: '🏠', label: 'Home' },
    { id: 'profile', icon: '👤', label: 'Profile' },
    { id: 'card', icon: '💳', label: 'Card' },
    { id: 'shops', icon: '🏪', label: 'Shops' },
    { id: 'transactions', icon: '📋', label: 'History' }
  ];
  @Input() activeItem = 'home';
  @Output() itemClicked = new EventEmitter<string>();

  onItemClick(itemId: string): void {
    this.itemClicked.emit(itemId);
  }

  isActive(itemId: string): boolean {
    return this.activeItem === itemId;
  }
}