.bottom-navigation {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: #2a2a2a;
  border-top: 1px solid #3a3a3a;
  padding: 8px 0;
  position: sticky;
  bottom: 0;
  width: 100%;
  z-index: 100;

  .nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px 4px;
    transition: all 0.3s ease;
    min-width: 0;

    .icon {
      font-size: 20px;
      margin-bottom: 2px;
    }

    .label {
      font-size: 10px;
      margin-top: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    &:hover {
      color: #888;
    }

    &.active {
      color: #e53935;
    }

    &:focus {
      outline: none;
    }
  }
}