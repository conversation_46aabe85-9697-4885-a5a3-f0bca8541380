.partner-card-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;

  .card-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px 0;
    background-color: #c62828;

    .partner-logo {
      color: white;
      font-weight: 700;
      font-size: 24px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
  }

  .card-content {
    flex: 1;
    padding: 20px;
    cursor: pointer;
    
    .user-info {
      text-align: center;
      margin-bottom: 25px;

      .greeting {
        font-size: 20px;
        font-weight: 300;
        margin: 0 0 8px;
      }

      .balance {
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;

        .label {
          color: #aaa;
        }

        .amount {
          font-weight: 600;
        }

        .fireworks {
          font-size: 20px;
          animation: pulse 1s ease-in-out infinite;
        }
      }
    }

    .history-section {
      .section-title {
        font-size: 16px;
        font-weight: 500;
        margin: 0 0 15px;
        color: #ccc;
      }

      .transactions-list {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .transaction-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px;
          background-color: #2a2a2a;
          border-radius: 6px;

          .transaction-info {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .date {
              font-size: 11px;
              color: #888;
            }

            .description {
              font-size: 13px;
              color: #ccc;
            }
          }

          .transaction-amount {
            color: #f44336;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .bottom-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #2a2a2a;
    border-top: 1px solid #3a3a3a;
    padding: 8px 0;
    position: sticky;
    bottom: 0;

    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      color: #666;
      cursor: pointer;
      padding: 8px;
      transition: all 0.3s ease;

      .icon {
        font-size: 20px;
        margin-bottom: 2px;
      }

      &:hover {
        color: #888;
      }

      &.active {
        color: #ffc107;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}