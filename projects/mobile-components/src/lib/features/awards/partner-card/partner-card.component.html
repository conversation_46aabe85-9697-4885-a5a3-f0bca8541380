<div class="partner-card-container">
  <div class="card-header" [style.background-color]="cardInfo.backgroundColor">
    <div class="partner-logo">{{ cardInfo.partnerLogo }}</div>
  </div>
  
  <div class="card-content" (click)="onCardClick()">
    <div class="user-info">
      <h2 class="greeting">Hello {{ cardInfo.userName }}</h2>
      <div class="balance">
        <span class="label">Balance: </span>
        <span class="amount">{{ cardInfo.currency }} {{ formatBalance(cardInfo.balance) }}</span>
        <span class="fireworks">🎆</span>
      </div>
    </div>

    <div class="history-section">
      <h3 class="section-title">History</h3>
      <div class="transactions-list">
        <div *ngFor="let transaction of getRecentTransactions()" class="transaction-item">
          <div class="transaction-info">
            <span class="date">{{ transaction.date }}</span>
            <span class="description">{{ transaction.description }}</span>
          </div>
          <div class="transaction-amount">
            <span>{{ transaction.currency }} {{ formatTransactionAmount(transaction.amount) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <nav class="bottom-nav">
    <button class="nav-item active" (click)="onNavigate('home')">
      <span class="icon">🏠</span>
    </button>
    <button class="nav-item" (click)="onNavigate('profile')">
      <span class="icon">👤</span>
    </button>
    <button class="nav-item" (click)="onNavigate('card')">
      <span class="icon">💳</span>
    </button>
    <button class="nav-item" (click)="onNavigate('shops')">
      <span class="icon">🏪</span>
    </button>
    <button class="nav-item" (click)="onNavigate('transactions')">
      <span class="icon">📋</span>
    </button>
  </nav>
</div>