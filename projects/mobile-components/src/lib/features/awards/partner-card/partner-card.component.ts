import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

export interface PartnerCardInfo {
  partnerId: string;
  partnerName: string;
  partnerLogo: string;
  backgroundColor: string;
  userName: string;
  balance: number;
  currency: string;
  transactions: Array<{
    date: string;
    description: string;
    amount: number;
    currency: string;
  }>;
}

@Component({
  selector: 'app-partner-card',
  templateUrl: './partner-card.component.html',
  styleUrls: ['./partner-card.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule]
})
export class PartnerCardComponent implements OnInit {
  @Input() cardInfo: PartnerCardInfo = {
    partnerId: 'buildit',
    partnerName: 'Build It',
    partnerLogo: 'Build IT',
    backgroundColor: '#c62828',
    userName: 'Marius',
    balance: 2116,
    currency: 'R',
    transactions: []
  };
  @Output() navigationClicked = new EventEmitter<string>();
  @Output() cardClicked = new EventEmitter<void>();

  ngOnInit(): void {
    // Initialize with recent transactions if none provided
    if (this.cardInfo.transactions.length === 0) {
      this.cardInfo.transactions = [
        {
          date: '09/01/2025',
          description: 'Purchase',
          amount: -1250.00,
          currency: 'R'
        },
        {
          date: '12/01/2025',
          description: 'Purchase',
          amount: -800.00,
          currency: 'R'
        },
        {
          date: '25/01/2025',
          description: 'Purchase',
          amount: -1100.00,
          currency: 'R'
        }
      ];
    }
  }

  onNavigate(route: string): void {
    this.navigationClicked.emit(route);
  }

  onCardClick(): void {
    this.cardClicked.emit();
  }

  formatBalance(amount: number): string {
    return amount.toLocaleString('en-ZA');
  }

  getRecentTransactions() {
    return this.cardInfo.transactions.slice(0, 3);
  }

  // Helper method to format transaction amounts
  formatTransactionAmount(amount: number): string {
    return Math.abs(amount).toFixed(2);
  }
}