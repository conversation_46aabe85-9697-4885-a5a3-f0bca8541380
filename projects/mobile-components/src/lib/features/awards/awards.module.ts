import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import { OtpRequestComponent } from './otp-request/otp-request.component';
import { OtpVerificationComponent } from './otp-verification/otp-verification.component';
import { RegistrationFormComponent } from './registration-form/registration-form.component';
import { AwardsLoginComponent } from './login/login.component';
import { AwardsDashboardComponent } from './dashboard/dashboard.component';
import { BalanceDetailComponent } from './balance-detail/balance-detail.component';
import { BottomNavigationComponent } from './bottom-navigation/bottom-navigation.component';
import { PartnerCardComponent } from './partner-card/partner-card.component';
import { QrCardComponent } from './qr-card/qr-card.component';
import { ProgramSelectionComponent } from './program-selection/program-selection.component';
import { TermsAcceptanceComponent } from './terms-acceptance/terms-acceptance.component';
import { EnrollmentConfirmationComponent } from './enrollment-confirmation/enrollment-confirmation.component';
import { TransactionFlowComponent } from './transaction-flow/transaction-flow.component';

@NgModule({
  declarations: [
    OtpRequestComponent,
    OtpVerificationComponent,
    RegistrationFormComponent,
    AwardsLoginComponent,
    AwardsDashboardComponent,
    BalanceDetailComponent,
    BottomNavigationComponent,
    PartnerCardComponent,
    QrCardComponent,
    ProgramSelectionComponent,
    TermsAcceptanceComponent,
    EnrollmentConfirmationComponent,
    TransactionFlowComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  exports: [
    OtpRequestComponent,
    OtpVerificationComponent,
    RegistrationFormComponent,
    AwardsLoginComponent,
    AwardsDashboardComponent,
    BalanceDetailComponent,
    BottomNavigationComponent,
    PartnerCardComponent,
    QrCardComponent,
    ProgramSelectionComponent,
    TermsAcceptanceComponent,
    EnrollmentConfirmationComponent,
    TransactionFlowComponent
  ]
})
export class AwardsModule { }