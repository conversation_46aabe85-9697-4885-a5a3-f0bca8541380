.transaction-flow-container {
  display: flex;
  min-height: 100vh;
  background-color: #f5f5f5;
  gap: 40px;
  padding: 20px;
  align-items: flex-start;

  .mobile-display {
    flex-shrink: 0;
    width: 300px;
    background-color: #1a1a1a;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    min-height: 600px;

    .card-wrapper {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;

      .card {
        width: 100%;
        max-width: 250px;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);

        .card-header {
          padding: 15px;
          text-align: center;
          background-color: rgba(0, 0, 0, 0.1);

          .partner-logo {
            color: white;
            font-weight: 700;
            font-size: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
          }
        }

        .card-body {
          padding: 15px;
          background-color: rgba(255, 255, 255, 0.95);
          color: #333;

          .user-info {
            text-align: center;
            margin-bottom: 15px;

            .user-name {
              font-size: 16px;
              font-weight: 600;
              margin: 0 0 6px;
              color: #1a1a1a;
            }

            .user-number {
              font-size: 14px;
              margin: 0;
              color: #666;
              letter-spacing: 1px;
            }
          }

          .qr-code-section {
            display: flex;
            justify-content: center;
            padding: 15px 0;

            .qr-code {
              background-color: white;
              padding: 15px;
              border-radius: 8px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

              .qr-placeholder {
                margin: 0;
                font-family: monospace;
                font-size: 10px;
                line-height: 1.2;
                color: #000;
                white-space: pre;
              }
            }
          }
        }
      }
    }

    .bottom-nav {
      display: flex;
      justify-content: space-around;
      align-items: center;
      background-color: #2a2a2a;
      border-top: 1px solid #3a3a3a;
      padding: 8px 0;

      .nav-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: none;
        border: none;
        color: #666;
        cursor: pointer;
        padding: 8px;
        transition: all 0.3s ease;

        .icon {
          font-size: 18px;
          margin-bottom: 2px;
        }

        &:hover {
          color: #888;
        }

        &.active {
          color: #ffc107;
        }
      }
    }
  }

  .transaction-details {
    flex: 1;
    background-color: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    .basket-icon {
      font-size: 48px;
      margin-bottom: 20px;
    }

    .transaction-title {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 30px;
      color: #333;
    }

    .steps-list {
      .step {
        font-size: 16px;
        line-height: 1.8;
        color: #555;
        margin: 0 0 10px;

        &:empty {
          height: 10px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: 20px;

    .mobile-display {
      width: 100%;
      max-width: 320px;
    }

    .transaction-details {
      width: 100%;
      padding: 20px;
    }
  }
}