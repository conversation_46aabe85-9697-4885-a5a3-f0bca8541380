<div class="transaction-flow-container">
  <div class="mobile-display">
    <div class="card-wrapper">
      <div class="card" [style.background-color]="transactionInfo.backgroundColor">
        <div class="card-header">
          <div class="partner-logo">{{ transactionInfo.partnerLogo }}</div>
        </div>
        
        <div class="card-body">
          <div class="user-info">
            <h2 class="user-name">{{ transactionInfo.userName }}</h2>
            <p class="user-number">{{ transactionInfo.memberNumber }}</p>
          </div>
          
          <div class="qr-code-section">
            <div class="qr-code">
              <pre class="qr-placeholder">{{ qrCodePlaceholder }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>

    <nav class="bottom-nav">
      <button class="nav-item active" (click)="onNavigate('home')">
        <span class="icon">🏠</span>
      </button>
      <button class="nav-item" (click)="onNavigate('profile')">
        <span class="icon">👤</span>
      </button>
      <button class="nav-item" (click)="onNavigate('card')">
        <span class="icon">💳</span>
      </button>
      <button class="nav-item" (click)="onNavigate('shops')">
        <span class="icon">🏪</span>
      </button>
      <button class="nav-item" (click)="onNavigate('transactions')">
        <span class="icon">📋</span>
      </button>
    </nav>
  </div>

  <div class="transaction-details">
    <div class="basket-icon">🛒</div>
    
    <h2 class="transaction-title">{{ transactionTitle }}</h2>
    
    <div class="steps-list">
      <p *ngFor="let step of transactionSteps" class="step">{{ step }}</p>
    </div>
  </div>
</div>