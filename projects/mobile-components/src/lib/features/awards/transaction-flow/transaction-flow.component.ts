import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';

export interface TransactionInfo {
  type: 'accrual' | 'redemption';
  partnerId: string;
  partnerName: string;
  partnerLogo: string;
  backgroundColor: string;
  userName: string;
  memberNumber: string;
  pointsRate?: string;
  amount?: number;
  points?: number;
  status?: 'scanning' | 'processing' | 'success' | 'insufficient';
}

@Component({
  selector: 'app-transaction-flow',
  templateUrl: './transaction-flow.component.html',
  styleUrls: ['./transaction-flow.component.scss']
})
export class TransactionFlowComponent implements OnInit {
  @Input() transactionInfo: TransactionInfo = {
    type: 'accrual',
    partnerId: 'buildit',
    partnerName: 'Build It',
    partnerLogo: 'Build IT',
    backgroundColor: '#c62828',
    userName: 'Marius Wan<PERSON>burg',
    memberNumber: '1237896114',
    pointsRate: 'R100 gives 10 points'
  };
  @Output() navigationClicked = new EventEmitter<string>();
  @Output() qrScanned = new EventEmitter<void>();

  qrCodePlaceholder = '';

  ngOnInit(): void {
    this.generateQRPlaceholder();
  }

  private generateQRPlaceholder(): void {
    const pattern = [
      '█▀▀▀▀▀█ ▄▀▄ █▀▀▀▀▀█',
      '█ ███ █ ▀▄▀ █ ███ █',
      '█ ▀▀▀ █ ▄▀▄ █ ▀▀▀ █',
      '▀▀▀▀▀▀▀ █▄█ ▀▀▀▀▀▀▀',
      '█▀▄ ▄▀▀▄▀▄▀▄█▀█▄▀▄█',
      '▀▄█▄▀▀▀ ▄█▄ ▀▄█▄▀▀▄',
      '█▀▀▀▀▀█ ▄▀▄ █▄▀█▄▀█',
      '█ ███ █ ▀▄▀ ▄█▄▀▄█▄',
      '█ ▀▀▀ █ ▄▀▄ ▀▄█▄▀▄█',
      '▀▀▀▀▀▀▀ ▀▀▀ ▀▀▀▀▀▀▀'
    ];
    this.qrCodePlaceholder = pattern.join('\n');
  }

  onNavigate(route: string): void {
    this.navigationClicked.emit(route);
  }

  get transactionTitle(): string {
    return this.transactionInfo.type === 'accrual' 
      ? 'IQRETAIL – Accrual – Award Points'
      : 'IQRETAIL – Redemption – using points to pay';
  }

  get transactionSteps(): string[] {
    if (this.transactionInfo.type === 'accrual') {
      return [
        'Cashier loads items',
        'Cashier scans QR Code',
        '',
        'Transaction details sends via API to loyalty program',
        `Program calculates points based on rules ie ${this.transactionInfo.pointsRate}`
      ];
    } else {
      return [
        'Cashier loads items',
        'Cashier scans QR Code as method of payment',
        '',
        'Details sends via API to loyalty program.',
        'Enough points available – Affirmative – Invoice paid – release goods'
      ];
    }
  }
}