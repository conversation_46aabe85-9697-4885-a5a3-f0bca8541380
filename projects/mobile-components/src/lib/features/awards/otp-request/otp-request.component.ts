import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-otp-request',
  templateUrl: './otp-request.component.html',
  styleUrls: ['./otp-request.component.scss']
})
export class OtpRequestComponent {
  @Input() logoSrc = 'assets/images/logo.png';
  @Output() otpRequested = new EventEmitter<string>();
  
  otpForm: FormGroup;
  loading = false;

  constructor(private fb: FormBuilder) {
    this.otpForm = this.fb.group({
      mobile: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]]
    });
  }

  onRequestOtp(): void {
    if (this.otpForm.valid) {
      this.loading = true;
      const mobile = this.otpForm.get('mobile')?.value;
      this.otpRequested.emit(mobile);
    }
  }

  get mobileControl() {
    return this.otpForm.get('mobile');
  }
}