.otp-request-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;
  padding: 20px;

  .header {
    display: flex;
    justify-content: center;
    padding: 40px 0;

    .logo {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: #333;
      padding: 10px;
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 400px;
    width: 100%;
    margin: 0 auto;

    .title {
      font-size: 24px;
      font-weight: 600;
      text-align: center;
      margin: 0 0 8px;
    }

    .subtitle {
      font-size: 14px;
      color: #888;
      text-align: center;
      margin: 0 0 32px;
    }

    .form-group {
      margin-bottom: 20px;

      .form-control {
        width: 100%;
        padding: 16px;
        background-color: #2a2a2a;
        border: 1px solid #3a3a3a;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
        transition: all 0.3s ease;

        &::placeholder {
          color: #666;
        }

        &:focus {
          outline: none;
          border-color: #e53935;
          background-color: #333;
        }

        &.error {
          border-color: #f44336;
        }
      }

      .error-message {
        color: #f44336;
        font-size: 12px;
        margin-top: 4px;
      }
    }

    .btn-primary {
      width: 100%;
      padding: 16px;
      background-color: #e53935;
      color: #ffffff;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;

      &:hover:not(:disabled) {
        background-color: #c62828;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}