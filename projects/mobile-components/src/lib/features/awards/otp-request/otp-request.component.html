<div class="otp-request-container">
  <div class="header">
    <img [src]="logoSrc" alt="Logo" class="logo">
  </div>
  
  <div class="content">
    <h2 class="title">Register</h2>
    <p class="subtitle">OTP Request</p>
    
    <form [formGroup]="otpForm" (ngSubmit)="onRequestOtp()">
      <div class="form-group">
        <input 
          type="tel" 
          formControlName="mobile" 
          placeholder="Mobile Number"
          class="form-control"
          maxlength="10"
          [class.error]="mobileControl?.invalid && mobileControl?.touched">
        
        <div class="error-message" *ngIf="mobileControl?.invalid && mobileControl?.touched">
          <span *ngIf="mobileControl?.errors?.['required']">Mobile number is required</span>
          <span *ngIf="mobileControl?.errors?.['pattern']">Please enter a valid 10-digit mobile number</span>
        </div>
      </div>
      
      <button 
        type="submit" 
        class="btn-primary"
        [disabled]="otpForm.invalid || loading">
        {{ loading ? 'Requesting...' : 'Request OTP' }}
      </button>
    </form>
  </div>
</div>