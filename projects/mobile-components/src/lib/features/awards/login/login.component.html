<div class="login-container">
  <div class="header">
    <img [src]="logoSrc" alt="Logo" class="logo">
  </div>
  
  <div class="content">
    <form [formGroup]="loginForm" (ngSubmit)="onLogin()">
      <div class="form-group">
        <input 
          type="tel" 
          formControlName="mobile" 
          placeholder="MOBILE NUMBER:"
          class="form-control"
          maxlength="10"
          [class.error]="mobileControl?.invalid && mobileControl?.touched">
      </div>

      <div class="form-group">
        <div class="password-input-wrapper">
          <input 
            [type]="passwordVisible ? 'text' : 'password'" 
            formControlName="password" 
            placeholder="PASSWORD"
            class="form-control"
            [class.error]="passwordControl?.invalid && passwordControl?.touched">
          <button 
            type="button"
            class="password-toggle"
            (click)="togglePasswordVisibility()">
            <span class="icon">{{ passwordVisible ? '👁️' : '👁️‍🗨️' }}</span>
          </button>
        </div>
      </div>
      
      <button 
        type="submit" 
        class="btn-primary"
        [disabled]="loginForm.invalid || loading">
        {{ loading ? 'Logging in...' : 'Login' }}
      </button>
    </form>
  </div>

  <nav class="bottom-nav">
    <button class="nav-item active" (click)="onNavigate('home')">
      <span class="icon">🏠</span>
    </button>
    <button class="nav-item" (click)="onNavigate('locations')">
      <span class="icon">🏪</span>
    </button>
    <button class="nav-item" (click)="onNavigate('transactions')">
      <span class="icon">📋</span>
    </button>
  </nav>
</div>