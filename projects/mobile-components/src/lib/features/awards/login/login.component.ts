import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

export interface LoginCredentials {
  mobile: string;
  password: string;
}

@Component({
  selector: 'app-awards-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class AwardsLoginComponent {
  @Input() logoSrc = 'assets/images/logo.png';
  @Output() loginSubmitted = new EventEmitter<LoginCredentials>();
  @Output() navigationClicked = new EventEmitter<string>();
  
  loginForm: FormGroup;
  loading = false;
  passwordVisible = false;

  constructor(private fb: FormBuilder) {
    this.loginForm = this.fb.group({
      mobile: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  onLogin(): void {
    if (this.loginForm.valid) {
      this.loading = true;
      const credentials: LoginCredentials = this.loginForm.value;
      this.loginSubmitted.emit(credentials);
    }
  }

  togglePasswordVisibility(): void {
    this.passwordVisible = !this.passwordVisible;
  }

  onNavigate(route: string): void {
    this.navigationClicked.emit(route);
  }

  get mobileControl() {
    return this.loginForm.get('mobile');
  }

  get passwordControl() {
    return this.loginForm.get('password');
  }
}