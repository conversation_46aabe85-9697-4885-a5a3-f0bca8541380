import {
  Component,
  Input,
  ViewChild,
  ElementRef,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  ContentChild,
  TemplateRef,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  forwardRef
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../base/icon/icon.component';
import { BasePlaceloadComponent } from '../../base/placeload/placeload.component';

@Component({
  selector: 'base-input-number',
  templateUrl: './input-number.component.html',
  styleUrls: ['./input-number.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconComponent,
    BasePlaceloadComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputNumberComponent),
      multi: true,
    },
  ],
})
export class InputNumberComponent
  implements OnInit, OnDestroy, ControlValueAccessor
{
  // Standard Tailwind class inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() contrast: 'default' | 'default-contrast' | 'muted' | 'muted-contrast' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() placeholder: string = '0';
  @Input() label: string = 'Number';
  @Input() helpText: string = '';
  @Input() errorMessage: string = '';

  // Number-specific inputs
  @Input() min?: number;
  @Input() max?: number;
  @Input() step: number = 1;
  @Input() precision: number = 0;
  @Input() currency: string = '';
  @Input() currencyDisplay: 'symbol' | 'code' | 'name' = 'symbol';
  @Input() locale: string = 'en-US';
  @Input() showControls: boolean = true;
  @Input() showValidation: boolean = true;
  @Input() allowNegative: boolean = true;
  @Input() format: 'decimal' | 'currency' | 'percent' = 'decimal';

  // Legacy inputs for backward compatibility
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() id?: string;
  @Input() type: string = 'text';
  @Input() inputmode?: 'numeric' | 'decimal';
  @Input() labelFloat?: boolean;
  @Input() icon?: string;
  @Input() error: string | boolean = false;
  @Input() iconDecrement: string = 'lucide:minus';
  @Input() iconIncrement: string = 'lucide:plus';
  @Input() colorFocus?: boolean;
  @Input() loading?: boolean;
  @Input() classes: {
    wrapper?: string | string[];
    outer?: string | string[];
    label?: string | string[];
    input?: string | string[];
    addon?: string | string[];
    error?: string | string[];
    icon?: string | string[];
    buttons?: string | string[];
  } = {};

  @ViewChild('inputRef') inputRef!: ElementRef<HTMLInputElement>;
  @Output() modelChange = new EventEmitter<number>();
  @ContentChild('labelTemplate') labelTemplate?: TemplateRef<any>;

  modelValue: number | undefined;

  radiuses: Record<string, string> = {
    none: '',
    sm: 'nui-input-number-rounded-sm',
    md: 'nui-input-number-rounded-md',
    lg: 'nui-input-number-rounded-lg',
    full: 'nui-input-number-rounded-full',
  };

  sizes: Record<string, string> = {
    sm: 'nui-input-number-sm',
    md: 'nui-input-number-md',
    lg: 'nui-input-number-lg',
  };

  contrasts: Record<string, string> = {
    default: 'nui-input-number-default',
    'default-contrast': 'nui-input-number-default-contrast',
    muted: 'nui-input-number-muted',
    'muted-contrast': 'nui-input-number-muted-contrast',
  };

  defaultInputmode: string = 'numeric';

  private incrementInterval: any;
  private decrementInterval: any;

  ngOnInit() {
    if (!this.id) {
      this.id = `input-number-${Math.random().toString(36).substring(2, 11)}`;
    }
  }

  ngOnDestroy() {
    this.clearIntervals();
  }

  get computedPlaceholder(): string | undefined {
    if (this.loading) {
      return undefined;
    }
    if (this.labelFloat) {
      return this.label;
    }
    return this.placeholder;
  }

  private looseToNumber(val: any): number {
    const n = Number.parseFloat(val);
    return Number.isNaN(n) ? 0 : n;
  }

  private clamp(value: number): number {
    const precision = this.getFloatPrecision();
    const rounded = Math.round(value * 10 ** precision) / 10 ** precision;
    return Math.max(
      Math.min(rounded, this.max ?? Number.POSITIVE_INFINITY),
      this.min ?? Number.NEGATIVE_INFINITY
    );
  }

  private getFloatPrecision(): number {
    if (!Number.isFinite(this.step) || Number.isNaN(this.step)) return 0;
    let exp = 1;
    let precision = 0;
    while (Math.round(this.step * exp) / exp !== this.step) {
      exp *= 10;
      precision++;
    }
    return precision;
  }

  increment() {
    if (this.disabled) return;
    this.modelValue =
      this.modelValue === undefined
        ? 0
        : this.clamp(this.modelValue + Math.abs(this.step));
    this.onChange(this.modelValue);
  }

  decrement() {
    if (this.disabled) return;
    this.modelValue =
      this.modelValue === undefined
        ? 0
        : this.clamp(this.modelValue - Math.abs(this.step));
    this.onChange(this.modelValue);
  }

  startIncrement() {
    if (this.disabled) return;
    this.increment();
    let i = 0;
    this.incrementInterval = setInterval(() => {
      i++;
      this.increment();
      if (i > 10) {
        clearInterval(this.incrementInterval);
        this.incrementInterval = setInterval(() => this.increment(), 50);
      }
    }, 150);
  }

  stopIncrement() {
    clearInterval(this.incrementInterval);
  }

  startDecrement() {
    if (this.disabled) return;
    this.decrement();
    let i = 0;
    this.decrementInterval = setInterval(() => {
      i++;
      this.decrement();
      if (i > 10) {
        clearInterval(this.decrementInterval);
        this.decrementInterval = setInterval(() => this.decrement(), 50);
      }
    }, 150);
  }

  stopDecrement() {
    clearInterval(this.decrementInterval);
  }

  private clearIntervals() {
    clearInterval(this.incrementInterval);
    clearInterval(this.decrementInterval);
  }

  // ControlValueAccessor methods
  onChange: any = () => {};
  onTouched: any = () => {};

  writeValue(value: any): void {
    this.modelValue = this.looseToNumber(value);
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  isErrorString(): boolean {
    return typeof this.error === 'string';
  }
}
