import { Component, Input, Output, EventEmitter, OnInit, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

export interface Industry {
  code: string;
  name: string;
  category?: string;
  disabled?: boolean;
}

@Component({
  selector: 'base-industry-select',
  templateUrl: './industry-select.component.html',
  styleUrls: ['./industry-select.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: IndustrySelectComponent,
      multi: true,
    },
  ],
})
export class IndustrySelectComponent implements OnInit, ControlValueAccessor {
  // Standard Tailwind class inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() placeholder: string = 'Select an industry';
  @Input() label: string = 'Industry';
  @Input() helpText: string = '';
  @Input() errorMessage: string = '';

  // Form-specific inputs
  @Input() industries: Industry[] = [];
  @Input() searchable: boolean = true;
  @Input() clearable: boolean = true;
  @Input() allowMultiple: boolean = false;
  @Input() groupByCategory: boolean = false;
  @Input() showValidation: boolean = true;

  // Event outputs
  @Output() valueChange = new EventEmitter<string | string[]>();
  @Output() industrySelect = new EventEmitter<Industry>();
  @Output() validationChange = new EventEmitter<boolean>();

  // Internal state
  selectedValue: string | string[] = '';
  searchTerm: string = '';
  isOpen: boolean = false;
  filteredIndustries: Industry[] = [];
  touched: boolean = false;
  errors: string[] = [];

  // Form control integration
  onChange: any = () => {};
  onTouched: any = () => {};

  // Default industries
  private defaultIndustries: Industry[] = [
    { code: 'tech', name: 'Technology', category: 'Technology' },
    { code: 'finance', name: 'Financial Services', category: 'Finance' },
    { code: 'healthcare', name: 'Healthcare', category: 'Healthcare' },
    { code: 'education', name: 'Education', category: 'Education' },
    { code: 'retail', name: 'Retail', category: 'Commerce' },
    { code: 'manufacturing', name: 'Manufacturing', category: 'Industry' },
    { code: 'media', name: 'Media & Entertainment', category: 'Media' },
    { code: 'consulting', name: 'Consulting', category: 'Professional Services' },
    { code: 'real-estate', name: 'Real Estate', category: 'Real Estate' },
    { code: 'automotive', name: 'Automotive', category: 'Industry' }
  ];

  // Computed classes using signals
  private sizeClasses = signal({
    xs: 'text-xs px-2 py-1',
    sm: 'text-sm px-3 py-1.5',
    md: 'text-base px-3 py-2',
    lg: 'text-lg px-4 py-2.5',
    xl: 'text-xl px-4 py-3'
  });

  private variantClasses = signal({
    default: 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
    primary: 'border-blue-300 focus:border-blue-600 focus:ring-blue-600',
    secondary: 'border-gray-400 focus:border-gray-600 focus:ring-gray-600',
    success: 'border-green-300 focus:border-green-500 focus:ring-green-500',
    warning: 'border-yellow-300 focus:border-yellow-500 focus:ring-yellow-500',
    danger: 'border-red-300 focus:border-red-500 focus:ring-red-500'
  });

  computedClasses = computed(() => {
    const sizeClass = this.sizeClasses()[this.size] || this.sizeClasses()['md'];
    const variantClass = this.variantClasses()[this.variant] || this.variantClasses()['default'];
    const disabledClass = this.disabled ? 'opacity-50 cursor-not-allowed' : '';
    const errorClass = this.hasError() ? 'border-red-500 focus:ring-red-500' : '';
    
    return `${sizeClass} ${variantClass} ${disabledClass} ${errorClass} ${this.className}`.trim();
  });

  ngOnInit(): void {
    this.initializeIndustries();
    this.setDefaultValues();
  }

  private initializeIndustries(): void {
    this.filteredIndustries = this.industries.length > 0 ? this.industries : this.defaultIndustries;
  }

  private setDefaultValues(): void {
    if (!this.selectedValue) {
      this.selectedValue = this.allowMultiple ? ['tech'] : 'tech';
    }
  }

  writeValue(value: string | string[]): void {
    this.selectedValue = value || (this.allowMultiple ? [] : '');
    this.validateValue();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  onSelect(industry: Industry): void {
    if (industry.disabled || this.disabled) return;

    if (this.allowMultiple) {
      const currentSelection = Array.isArray(this.selectedValue) ? this.selectedValue : [];
      const index = currentSelection.indexOf(industry.code);
      
      if (index > -1) {
        currentSelection.splice(index, 1);
      } else {
        currentSelection.push(industry.code);
      }
      
      this.selectedValue = currentSelection;
    } else {
      this.selectedValue = industry.code;
      this.isOpen = false;
    }

    this.onChange(this.selectedValue);
    this.valueChange.emit(this.selectedValue);
    this.industrySelect.emit(industry);
    this.validateValue();
  }

  toggleDropdown(): void {
    if (!this.disabled) {
      this.isOpen = !this.isOpen;
    }
  }

  onBlur(): void {
    this.touched = true;
    this.onTouched();
    this.validateValue();
    setTimeout(() => this.isOpen = false, 200);
  }

  private validateValue(): void {
    this.errors = [];
    if (this.required) {
      if (this.allowMultiple) {
        if (!Array.isArray(this.selectedValue) || this.selectedValue.length === 0) {
          this.errors.push('At least one industry must be selected');
        }
      } else {
        if (!this.selectedValue) {
          this.errors.push('Industry is required');
        }
      }
    }
    this.validationChange.emit(this.errors.length === 0);
  }

  hasError(): boolean {
    return this.errors.length > 0 && this.touched;
  }

  getErrorMessage(): string {
    return this.hasError() ? (this.errorMessage || this.errors[0] || '') : '';
  }

  isSelected(industry: Industry): boolean {
    if (this.allowMultiple) {
      return Array.isArray(this.selectedValue) && this.selectedValue.includes(industry.code);
    }
    return this.selectedValue === industry.code;
  }

  getSelectedDisplay(): string {
    if (this.allowMultiple) {
      const selectedArray = Array.isArray(this.selectedValue) ? this.selectedValue : [];
      if (selectedArray.length === 0) return this.placeholder;
      if (selectedArray.length === 1) {
        const industry = this.getIndustryByCode(selectedArray[0]);
        return industry ? industry.name : selectedArray[0];
      }
      return `${selectedArray.length} industries selected`;
    } else {
      if (!this.selectedValue) return this.placeholder;
      const industry = this.getIndustryByCode(this.selectedValue as string);
      return industry ? industry.name : this.selectedValue as string;
    }
  }

  getIndustryByCode(code: string): Industry | undefined {
    return this.filteredIndustries.find(industry => industry.code === code);
  }

  // Make Array available in template
  get Array() {
    return Array;
  }
}