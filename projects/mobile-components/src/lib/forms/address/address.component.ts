import {
  Component,
  Inject,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnChanges,
  SimpleChanges,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  computed,
  signal
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  Address,
  CodeItem,
  CountryItem,
  LssConfig,
  SystemService,
  ValidationService,
} from 'lp-client-api';
import { BehaviorSubject, Observable } from 'rxjs';
import { shareReplay } from 'rxjs/operators';

export interface AddressFormData {
  country: string;
  province: string;
  city: string;
  place: string;
  line1: string;
  line2: string;
  postalCode: string;
}

@Component({
  selector: 'lp-pos-address',
  templateUrl: './address.component.html',
  styleUrls: ['./address.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    IonicModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class AddressComponent implements OnInit, OnChanges {
  // Standard Tailwind class inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() placeholder: string = 'Enter address details';
  @Input() label: string = 'Address';
  @Input() helpText: string = '';
  @Input() errorMessage: string = '';

  // Form-specific inputs
  @Input() showLine2: boolean = true;
  @Input() showPostalCode: boolean = true;
  @Input() showSuburb: boolean = true;
  @Input() countryRequired: boolean = true;
  @Input() provinceRequired: boolean = true;
  @Input() cityRequired: boolean = true;
  @Input() line1Required: boolean = true;
  @Input() validationMessages: { [key: string]: string } = {};
  @Input() countries: CountryItem[] = [];
  @Input() autoComplete: boolean = true;
  @Input() showSubmitButton: boolean = false;

  // Legacy inputs for backward compatibility
  @Input() type: any;
  _data!: Address;
  @Input() required_field = false;
  @Input() mainForm!: FormGroup;
  @Input() mainAddress!: Address;

  // Event outputs
  @Output() valueChange = new EventEmitter<AddressFormData>();
  @Output() countryChange = new EventEmitter<string>();
  @Output() provinceChange = new EventEmitter<string>();
  @Output() cityChange = new EventEmitter<string>();
  @Output() validationChange = new EventEmitter<boolean>();
  @Output() formSubmit = new EventEmitter<AddressFormData>();

  // Create BehaviorSubjects at the class level
  private countriesSubject = new BehaviorSubject<CountryItem[]>([]);
  countriesObservable: Observable<CountryItem[]> = this.countriesSubject.asObservable().pipe(shareReplay(1));

  private provincesSubject = new BehaviorSubject<CodeItem[]>([]);
  provinces: Observable<CodeItem[]> = this.provincesSubject.asObservable().pipe(shareReplay(1));

  private districtsSubject = new BehaviorSubject<CodeItem[]>([]);
  districts: Observable<CodeItem[]> = this.districtsSubject.asObservable().pipe(shareReplay(1));

  private placesSubject = new BehaviorSubject<CodeItem[]>([]);
  places: Observable<CodeItem[]> = this.placesSubject.asObservable().pipe(shareReplay(1));

  provincesArray: CodeItem[] = [];
  districtsArray: CodeItem[] = [];
  cities!: CodeItem[];
  placesArray: CodeItem[] = [];
  @Input()
  modalCloseText = 'Close';
  @Input()
  modalCloseButtonSlot: 'start' | 'end' | 'primary' | 'secondary' = 'end';
  currentSelection = {
    country: '',
    province: '',
    district: '',
    city: '',
    places: '',
    line1: '',
    line2: '',
    postalCode: '',
  };
  selectedCity?: CodeItem;
  useDistrict = false;
  countryList: unknown;
  _myForm: FormGroup;
  loading = false;

  // Track API data loading state
  isLoadingCountries = true;
  isUsingApiData = false;

  // Array to store countries directly
  countriesArray: CountryItem[] = [];

  // Hardcoded fallback countries
  private fallbackCountries: CountryItem[] = [
    { code: 'DE', country: 'Germany', productId: '', dialcode: '+49', isoCode: 'de' },
    { code: 'US', country: 'United States', productId: '', dialcode: '+1', isoCode: 'us' },
    { code: 'GB', country: 'United Kingdom', productId: '', dialcode: '+44', isoCode: 'gb' },
    { code: 'ZA', country: 'South Africa', productId: '', dialcode: '+27', isoCode: 'za' },
    { code: 'AU', country: 'Australia', productId: '', dialcode: '+61', isoCode: 'au' },
    { code: 'CA', country: 'Canada', productId: '', dialcode: '+1', isoCode: 'ca' },
    { code: 'FR', country: 'France', productId: '', dialcode: '+33', isoCode: 'fr' },
    { code: 'IT', country: 'Italy', productId: '', dialcode: '+39', isoCode: 'it' },
    { code: 'ES', country: 'Spain', productId: '', dialcode: '+34', isoCode: 'es' },
    { code: 'NL', country: 'Netherlands', productId: '', dialcode: '+31', isoCode: 'nl' }
  ];

  // Hardcoded fallback provinces for common countries
  private fallbackProvinces: { [country: string]: CodeItem[] } = {
    'ZA': [
      { value: 'GP', label: 'Gauteng' },
      { value: 'WC', label: 'Western Cape' },
      { value: 'EC', label: 'Eastern Cape' },
      { value: 'NC', label: 'Northern Cape' },
      { value: 'NW', label: 'North West' },
      { value: 'FS', label: 'Free State' },
      { value: 'KZN', label: 'KwaZulu-Natal' },
      { value: 'MP', label: 'Mpumalanga' },
      { value: 'LP', label: 'Limpopo' }
    ],
    'US': [
      { value: 'AL', label: 'Alabama' },
      { value: 'AK', label: 'Alaska' },
      { value: 'AZ', label: 'Arizona' },
      { value: 'CA', label: 'California' },
      { value: 'CO', label: 'Colorado' },
      { value: 'NY', label: 'New York' },
      { value: 'TX', label: 'Texas' }
    ],
    'DE': [
      { value: 'BW', label: 'Baden-Württemberg' },
      { value: 'BY', label: 'Bavaria' },
      { value: 'BE', label: 'Berlin' },
      { value: 'BB', label: 'Brandenburg' },
      { value: 'HB', label: 'Bremen' },
      { value: 'HH', label: 'Hamburg' },
      { value: 'HE', label: 'Hesse' }
    ]
  };

  // Computed classes using signals
  private sizeClasses = signal({
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  });

  private variantClasses = signal({
    default: 'border-gray-300 focus:border-blue-500',
    primary: 'border-blue-300 focus:border-blue-600',
    secondary: 'border-gray-400 focus:border-gray-600',
    success: 'border-green-300 focus:border-green-500',
    warning: 'border-yellow-300 focus:border-yellow-500',
    danger: 'border-red-300 focus:border-red-500'
  });

  private roundedClasses = signal({
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    full: 'rounded-full'
  });

  // Computed class string
  computedClasses = computed(() => {
    const sizeClass = this.sizeClasses()[this.size] || this.sizeClasses()['md'];
    const variantClass = this.variantClasses()[this.variant] || this.variantClasses()['default'];
    const roundedClass = this.roundedClasses()[this.rounded] || this.roundedClasses()['md'];
    const disabledClass = this.disabled ? 'opacity-50 cursor-not-allowed' : '';
    
    return `${sizeClass} ${variantClass} ${roundedClass} ${disabledClass} ${this.className}`.trim();
  });

  constructor(
    private _formBuilder: FormBuilder,
    private _systemService: SystemService,
    public _formValidations: ValidationService,
    public lssConfig: LssConfig
  ) {
    console.log('AddressComponent: Constructor initialized');

    // Build form with dynamic validators based on inputs
    this._myForm = this._formBuilder.group({
      country: ['', this.countryRequired ? Validators.required : null],
      province: ['', this.provinceRequired ? Validators.required : null],
      city: ['', this.cityRequired ? Validators.required : null],
      place: ['', this.required ? Validators.required : null],
      line1: ['', this.line1Required ? Validators.required : null],
      line2: [''],
      postalCode: [''],
    });

    // Subscribe to form value changes
    this._myForm.valueChanges.subscribe(value => {
      this.valueChange.emit(value);
      this.validationChange.emit(this._myForm.valid);
    });

    // Start with empty countries array and show loading state
    this.isLoadingCountries = true;
    this.isUsingApiData = false;

    // Don't initialize with fallback countries immediately
    // Instead, wait for API response first

    // Try to fetch countries from the correct API endpoint: group/LAND
    console.log('AddressComponent: Attempting to fetch countries from API using group/LAND endpoint');
    this._systemService.getCodeGroup('LAND').subscribe({
      next: (codeGroupData) => {
        console.log('AddressComponent: Countries API response received from group/LAND endpoint');
        this.isLoadingCountries = false;

        if (codeGroupData && codeGroupData.codeItem && codeGroupData.codeItem.length > 0) {
          console.log(`AddressComponent: Received ${codeGroupData.codeItem.length} countries from API`);

          // Mark that we're using API data
          this.isUsingApiData = true;

          // Convert LPCode items to CountryItem format
          const countryData: CountryItem[] = codeGroupData.codeItem.map(code => ({
            code: code.codeId,
            country: code.description,
            productId: '',
            dialcode: '',
            isoCode: code.codeId.toLowerCase()
          }));

          // Store in both the subject and local array
          this.countriesArray = countryData;
          this.countryList = countryData;
          this.countriesSubject.next(countryData);

          // If we already have a country selected from the address data, make sure it's still valid
          if (this.mainAddress && this.mainAddress.country) {
            this.validateSelectedCountry();
          }
        } else {
          console.warn('AddressComponent: No countries received from API or empty array, using fallback data');
          // Fall back to hardcoded countries if API returns empty data
          this.countriesArray = [...this.fallbackCountries];
          this.countryList = this.fallbackCountries;
          this.countriesSubject.next(this.fallbackCountries);
          console.log('AddressComponent: Using fallback countries due to empty API response');
        }
      },
      error: (error) => {
        console.error('AddressComponent: Error fetching countries from group/LAND endpoint:', error);
        console.error('AddressComponent: Error occurred while fetching countries from API');

        // Fall back to hardcoded countries if API call fails
        this.isLoadingCountries = false;
        this.countriesArray = [...this.fallbackCountries];
        this.countryList = this.fallbackCountries;
        this.countriesSubject.next(this.fallbackCountries);
        console.log('AddressComponent: Using fallback countries due to API error');
      }
    });

    // Use provided countries or fallback
    if (this.countries.length > 0) {
      this.countriesArray = this.countries;
      this.countriesSubject.next(this.countries);
      this.isLoadingCountries = false;
    }

    // Initialize the form with the mainAddress data if available
    if (this.mainAddress) {
      this.setDefaultValues();
    }
  }

  // Helper method to validate that the selected country is in the countries list
  private validateSelectedCountry(): void {
    if (this.mainAddress && this.mainAddress.country) {
      const countryCode = this.mainAddress.country;
      const countryExists = this.countriesArray.some(c =>
        c.code === countryCode || c.isoCode.toLowerCase() === countryCode.toLowerCase()
      );

      if (!countryExists) {
        console.warn(`AddressComponent: Selected country ${countryCode} not found in countries list`);
      } else {
        console.log(`AddressComponent: Selected country ${countryCode} validated in countries list`);
      }
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['mainAddress']) {
      if (this.mainAddress && this.mainAddress.country) {
        this.patchFormSeq();
      }
    }
  }

  ngOnInit(): void {
    if (this.mainForm != null) {
      this.mainForm.addControl(`address_${this.type}`, this._myForm);
    }

    // Initialize the form with the mainAddress data if available
    if (this.mainAddress) {
      this.patchFormSeq();
    }
  }

  // Find a country by ISO code or regular code
  findCountryByCode(code: string): Promise<CountryItem | undefined> {
    return new Promise((resolve) => {
      console.log(`AddressComponent: Finding country with code: ${code}`);

      // Always use the fallback countries if no countries are available
      const countries = this.countriesArray.length > 0 ? this.countriesArray : this.fallbackCountries;
      console.log(`AddressComponent: Using ${countries.length} countries for lookup`);

      // First try to find by exact code match
      let country = countries.find(c =>
        (this.lssConfig.useISO ? c.isoCode : c.code) === code
      );

      if (country) {
        console.log(`AddressComponent: Found country by exact match: ${country.country}`);
        resolve(country);
        return;
      }

      // If not found and it looks like an ISO code (2 letters), try to find by ISO code
      if (code && code.length === 2) {
        country = countries.find(c => c.isoCode.toLowerCase() === code.toLowerCase());
        if (country) {
          console.log(`AddressComponent: Found country by ISO code: ${country.country}`);
          resolve(country);
          return;
        }
      }

      // If still not found and we have a code that's not ISO format, try by regular code
      if (code && code.length !== 2) {
        country = countries.find(c => c.code === code);
        if (country) {
          console.log(`AddressComponent: Found country by regular code: ${country.country}`);
          resolve(country);
          return;
        }
      }

      // If still not found, try a case-insensitive search
      if (code) {
        country = countries.find(c =>
          c.code.toLowerCase() === code.toLowerCase() ||
          c.isoCode.toLowerCase() === code.toLowerCase()
        );
        if (country) {
          console.log(`AddressComponent: Found country by case-insensitive search: ${country.country}`);
          resolve(country);
          return;
        }
      }

      // If still not found, create a fallback country object for the given code
      if (code) {
        console.warn(`AddressComponent: No country found with code: ${code}, creating fallback`);
        const fallbackCountry: CountryItem = {
          code: code,
          country: `Country ${code}`,
          productId: '',
          dialcode: '',
          isoCode: code.toLowerCase()
        };
        resolve(fallbackCountry);
        return;
      }

      // If no code provided, resolve with undefined
      console.warn('AddressComponent: No country code provided');
      resolve(undefined);
    });
  }

  async patchFormSeq() {
    if (this.mainAddress == undefined) return;

    this.loading = true;

    if (this.mainAddress.country) {
      // Find the country in our list
      const country = await this.findCountryByCode(this.mainAddress.country);

      if (country) {
        // Get the correct value based on useISO setting
        const countryValue = this.lssConfig.useISO ? country.isoCode : country.code;

        // Set the country value and trigger the change
        this._myForm.controls['country'].patchValue(countryValue);
        await this.onCountryChange(countryValue);

        // Now handle the rest of the address fields
        if (this.mainAddress.province) {
          this._myForm.controls['province'].patchValue(this.mainAddress.province);
          this.onProvinceChange(this.mainAddress.province);

          if (this.useDistrict && this.mainAddress.district) {
            this.districtChange(this.mainAddress.district, this.mainAddress.province);
          }

          if (this.mainAddress.city) {
            this.onCityChange(this.mainAddress.city);
          }
        }

        // Set the remaining fields
        if (this.mainAddress.suburb) {
          this._myForm.controls['place'].patchValue(this.mainAddress.suburb);
        }

        if (this.mainAddress.line1) {
          this._myForm.controls['line1'].patchValue(this.mainAddress.line1);
        }

        if (this.mainAddress.line2) {
          this._myForm.controls['line2'].patchValue(this.mainAddress.line2);
        }

        if (this.mainAddress.postalCode) {
          this._myForm.controls['postalCode'].patchValue(this.mainAddress.postalCode);
        }
      } else {
        console.warn(`Country with code ${this.mainAddress.country} not found in the list`);
      }
    }

    this.loading = false;
  }
  get form(): any {
    return this._myForm.controls;
  }

  // This method is no longer needed since province is now a text input
  // Keeping a simplified version for backward compatibility
  private getFallbackProvinces(_countryCode: string): CodeItem[] {
    // No need to do anything since province is now a text input
    console.log(`AddressComponent: getFallbackProvinces called but not needed anymore`);
    return [];
  }

  // Set default values for demo/preview
  private setDefaultValues(): void {
    const defaultValues = {
      country: 'US',
      province: 'California',
      city: 'San Francisco',
      place: 'Downtown',
      line1: '123 Main Street',
      line2: 'Apt 4B',
      postalCode: '94105'
    };

    if (!this.mainAddress) {
      this._myForm.patchValue(defaultValues);
    }
  }

  async onCountryChange(country?: string) {
    if (country) {
      if (this.currentSelection.country === country) {
        return;
      }

      console.log(`AddressComponent: Country changed to: ${country}`);
      this.currentSelection.country = country;
      this.countryChange.emit(country);

      // Since province is now a text input, we don't need to do anything special
      // Just clear the province field if we're not in the loading process
      if (!this.loading) {
        this.form.province.patchValue('');
      }
    }
  }
  onProvinceChange(province?: string) {
    if (province) {
      if (this.currentSelection.province === province) {
        return;
      }
      this.currentSelection.province = province;
      this.provinceChange.emit(province);
      console.log(`AddressComponent: Province changed to: ${province}`);

      // Since city is now a text input, we don't need to do anything special
      // Just clear the city field if we're not in the loading process
      if (!this.loading) {
        this.form.city.patchValue('');
      }
    }
  }

  districtChange(district?: string, province?: string) {
    if (district) {
      this.populateCity(district, province);
    }
  }
  // This method is no longer needed since city is now a text input
  // Keeping a simplified version for backward compatibility
  populateCity(_district?: string, _province?: string) {
    // No need to do anything since city is now a text input
    console.log(`AddressComponent: populateCity called but not needed anymore`);
  }
  onCityChange(city?: string) {
    if (city) {
      console.log(`AddressComponent: City changed to: ${city}`);
      this.cityChange.emit(city);

      // Since place/suburb is now a text input, we don't need to do anything special
      // Just clear the place field if we're not in the loading process
      if (!this.loading) {
        this.form.place.patchValue('');
      }
    }
  }

  // Submit form
  onSubmit(): void {
    if (this._myForm.valid) {
      this.formSubmit.emit(this._myForm.value);
    }
  }

  // Get validation error message
  getErrorMessage(field: string): string {
    const control = this._myForm.get(field);
    if (control && control.errors && control.touched) {
      if (this.validationMessages[field]) {
        return this.validationMessages[field];
      }
      
      const errors = control.errors;
      if (errors['required']) {
        return `${field} is required`;
      }
      if (errors['email']) {
        return 'Please enter a valid email address';
      }
      if (errors['minlength']) {
        return `${field} must be at least ${errors['minlength'].requiredLength} characters`;
      }
      if (errors['maxlength']) {
        return `${field} cannot exceed ${errors['maxlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  // Check if field has error
  hasError(field: string): boolean {
    const control = this._myForm.get(field);
    return !!(control && control.errors && control.touched);
  }

  // Get field classes with validation states
  getFieldClasses(field: string): string {
    const baseClasses = this.computedClasses();
    const control = this._myForm.get(field);
    
    if (control) {
      if (control.errors && control.touched) {
        return `${baseClasses} border-red-500 focus:border-red-600`;
      }
      if (control.valid && control.touched) {
        return `${baseClasses} border-green-500 focus:border-green-600`;
      }
    }
    
    return baseClasses;
  }

  getEventValueFilter(event: any): any {
    this.selectedCity = event.item;
    if (event.item) {
      return event.item.value;
    }
    return null;
  }

  // Helper method to extract value from ion events
  getEventValue(event: any): string {
    return event.detail?.value || event.target?.value || '';
  }
}
