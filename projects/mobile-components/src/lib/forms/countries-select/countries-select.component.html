<div class="countries-select-container relative" [class]="computedClasses()">
  <!-- Label -->
  <label 
    *ngIf="label" 
    class="block text-sm font-medium text-gray-700 mb-2"
    [class.text-red-600]="hasError()"
  >
    {{ label }}
    <span *ngIf="required" class="text-red-500 ml-1">*</span>
  </label>

  <!-- Help Text -->
  <p *ngIf="helpText" class="text-sm text-gray-500 mb-2">
    {{ helpText }}
  </p>

  <!-- Main Select Button -->
  <div class="relative">
    <button
      type="button"
      (click)="toggleDropdown()"
      (focus)="onFocus($event)"
      (blur)="onBlur($event)"
      [disabled]="disabled"
      [class]="'w-full text-left border bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-0 ' + computedClasses()"
      [attr.aria-label]="label || 'Select country'"
      [attr.aria-required]="required"
      [attr.aria-invalid]="hasError()"
      [attr.aria-expanded]="isOpen"
      [attr.aria-describedby]="hasError() ? 'countries-error' : null"
    >
      <span class="flex items-center justify-between">
        <span class="flex items-center">
          <!-- Selected Flag(s) -->
          <span *ngIf="showFlags && !allowMultiple && selectedValue" class="mr-2">
            {{ getCountryByCode(getSelectedValueAsString())?.flag }}
          </span>
          
          <!-- Display Text -->
          <span class="block truncate" [class.text-gray-400]="!selectedValue || (Array.isArray(selectedValue) && selectedValue.length === 0)">
            {{ getSelectedDisplay() }}
          </span>
        </span>

        <!-- Clear Button -->
        <button
          *ngIf="clearable && selectedValue && ((Array.isArray(selectedValue) && selectedValue.length > 0) || (!Array.isArray(selectedValue) && selectedValue))"
          type="button"
          (click)="onClear(); $event.stopPropagation()"
          class="mr-2 text-gray-400 hover:text-gray-600 focus:outline-none"
          [attr.aria-label]="'Clear selection'"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>

        <!-- Dropdown Arrow -->
        <svg class="w-5 h-5 text-gray-400 transition-transform" [class.rotate-180]="isOpen" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </span>
    </button>

    <!-- Dropdown Panel -->
    <div
      *ngIf="isOpen"
      class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
      role="listbox"
      [attr.aria-label]="'Country options'"
    >
      <!-- Search Input -->
      <div *ngIf="searchable" class="p-2 border-b border-gray-200">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="onSearch(searchTerm)"
          class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          [placeholder]="'Search countries...'"
          [attr.aria-label]="'Search countries'"
        />
      </div>

      <!-- Countries List -->
      <div class="py-1">
        <!-- No Results -->
        <div *ngIf="filteredCountries.length === 0" class="px-3 py-2 text-sm text-gray-500">
          No countries found
        </div>

        <!-- Country Options -->
        <div
          *ngFor="let country of filteredCountries; trackBy: trackByCode"
          (click)="onSelect(country)"
          class="flex items-center px-3 py-2 cursor-pointer hover:bg-gray-100 focus:bg-gray-100"
          [class.bg-blue-50]="isSelected(country)"
          [class.opacity-50]="country.disabled"
          [class.cursor-not-allowed]="country.disabled"
          role="option"
          [attr.aria-selected]="isSelected(country)"
          [attr.aria-label]="country.name"
        >
          <!-- Checkbox for Multiple Selection -->
          <input
            *ngIf="allowMultiple"
            type="checkbox"
            [checked]="isSelected(country)"
            [disabled]="country.disabled"
            class="mr-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            readonly
          />

          <!-- Flag -->
          <span *ngIf="showFlags" class="mr-3 text-lg">
            {{ country.flag }}
          </span>

          <!-- Country Name -->
          <span class="flex-1 text-sm">{{ country.name }}</span>

          <!-- Country Code -->
          <span class="text-xs text-gray-500 ml-2">{{ country.code }}</span>

          <!-- Dial Code -->
          <span *ngIf="showDialCodes && country.dialCode" class="text-xs text-gray-400 ml-2">
            {{ country.dialCode }}
          </span>

          <!-- Selected Indicator for Single Selection -->
          <svg
            *ngIf="!allowMultiple && isSelected(country)"
            class="w-4 h-4 text-blue-600 ml-2"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>

      <!-- Multiple Selection Footer -->
      <div *ngIf="allowMultiple && Array.isArray(selectedValue) && selectedValue.length > 0" class="border-t border-gray-200 px-3 py-2 bg-gray-50">
        <div class="flex items-center justify-between text-sm">
          <span class="text-gray-600">
            {{ selectedValue.length }} {{ selectedValue.length === 1 ? 'country' : 'countries' }} selected
            <span *ngIf="maxSelections > 0"> (max {{ maxSelections }})</span>
          </span>
          <button
            type="button"
            (click)="onClear()"
            class="text-blue-600 hover:text-blue-800 focus:outline-none"
          >
            Clear all
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Error Message -->
  <div *ngIf="hasError() && showValidation" class="mt-2">
    <p 
      id="countries-error"
      class="text-sm text-red-600"
      role="alert"
      [attr.aria-live]="'polite'"
    >
      {{ getErrorMessage() }}
    </p>
  </div>

  <!-- General Error Message -->
  <div *ngIf="errorMessage && hasError()" class="mt-2">
    <div class="text-red-600 text-sm" role="alert" [attr.aria-live]="'polite'">
      {{ errorMessage }}
    </div>
  </div>
</div>