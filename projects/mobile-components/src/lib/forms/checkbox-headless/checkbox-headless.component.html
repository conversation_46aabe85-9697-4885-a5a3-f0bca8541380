<div [class]="'checkbox-headless-container ' + computedClasses()" role="group" [attr.aria-label]="label">
  <!-- Label -->
  <label
    *ngIf="label"
    [for]="id || item_id"
    class="block text-sm font-medium text-gray-700 mb-2"
    [class.text-red-600]="hasError()"
  >
    {{ label }}
    <span *ngIf="required" class="text-red-500 ml-1">*</span>
  </label>
  
  <!-- Help Text -->
  <p *ngIf="helpText" class="text-sm text-gray-500 mb-3">
    {{ helpText }}
  </p>
  
  <!-- Description -->
  <p *ngIf="description" class="text-sm text-gray-600 mb-3">
    {{ description }}
  </p>

  <!-- Single Checkbox (Legacy Support) -->
  <div *ngIf="!options.length" class="relative">
    <input
      [id]="id || item_id"
      #inputRef
      [(ngModel)]="modelValue"
      [value]="value"
      [attr.true-value]="trueValue"
      [attr.false-value]="falseValue"
      [disabled]="disabled"
      [required]="required"
      [indeterminate]="indeterminate"
      class="peer absolute inset-0 z-20 size-full cursor-pointer opacity-0"
      [class]="getFieldClasses()"
      type="checkbox"
      (change)="onCheckboxChange($event)"
      (focus)="onFocus($event)"
      (blur)="onBlur($event)"
      [attr.aria-label]="label || placeholder"
      [attr.aria-required]="required"
      [attr.aria-invalid]="hasError()"
      [attr.aria-describedby]="hasError() ? 'checkbox-error' : null"
    />
    <ng-content
      [ngTemplateOutlet]="contentTemplate"
      [ngTemplateOutletContext]="{ value: modelValue }"
    ></ng-content>
  </div>

  <!-- Multiple Options -->
  <div *ngIf="options.length" class="space-y-2" [class.flex]="inline" [class.space-x-4]="inline" [class.space-y-0]="inline">
    <div *ngFor="let option of options; trackBy: trackByValue" class="flex items-center">
      <input
        [id]="item_id + '_' + option.value"
        [value]="option.value"
        [checked]="isSelected(option)"
        [disabled]="disabled || option.disabled"
        [required]="required"
        type="checkbox"
        [class]="getFieldClasses()"
        class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
        (change)="toggleOption(option)"
        (focus)="onFocus($event)"
        (blur)="onBlur($event)"
        [attr.aria-label]="option.label"
        [attr.aria-required]="required"
        [attr.aria-invalid]="hasError()"
        [attr.aria-describedby]="hasError() ? 'checkbox-error' : null"
      />
      <label 
        [for]="item_id + '_' + option.value" 
        class="ml-2 text-sm text-gray-700 cursor-pointer select-none"
        [class.text-gray-400]="disabled || option.disabled"
        [class.cursor-not-allowed]="disabled || option.disabled"
      >
        {{ option.label }}
      </label>
    </div>
  </div>

  <!-- Error Message -->
  <div *ngIf="hasError() && showValidation" class="mt-2">
    <p 
      id="checkbox-error"
      class="text-sm text-red-600"
      role="alert"
      [attr.aria-live]="'polite'"
    >
      {{ getErrorMessage() }}
    </p>
  </div>

  <!-- General Error Message -->
  <div *ngIf="errorMessage && hasError()" class="mt-2">
    <div class="text-red-600 text-sm" role="alert" [attr.aria-live]="'polite'">
      {{ errorMessage }}
    </div>
  </div>
</div>