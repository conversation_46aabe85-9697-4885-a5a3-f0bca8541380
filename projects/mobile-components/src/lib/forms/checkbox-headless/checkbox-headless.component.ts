import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  ContentChild,
  TemplateRef,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  computed,
  signal
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

export interface CheckboxOption<T = any> {
  value: T;
  label: string;
  disabled?: boolean;
}

@Component({
  selector: 'base-checkbox-headless',
  templateUrl: './checkbox-headless.component.html',
  styleUrls: ['./checkbox-headless.component.css'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: CheckboxHeadlessComponent,
      multi: true,
    },
  ],
  standalone: true,
  imports: [CommonModule, FormsModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class CheckboxHeadlessComponent<T> implements ControlValueAccessor {
  // Standard Tailwind class inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() placeholder: string = '';
  @Input() label: string = '';
  @Input() helpText: string = '';
  @Input() errorMessage: string = '';

  // Form-specific inputs
  @Input() indeterminate: boolean = false;
  @Input() validationMessages: { [key: string]: string } = {};
  @Input() showValidation: boolean = true;
  @Input() validateOnChange: boolean = true;
  @Input() options: CheckboxOption<T>[] = [];
  @Input() multiple: boolean = false;
  @Input() inline: boolean = false;
  @Input() description: string = '';

  // Legacy inputs for backward compatibility
  @Input() value?: T;
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() trueValue: T = true as any;
  @Input() falseValue: T = false as any;
  @Input() id?: string;

  // Event outputs
  @Output() valueChange = new EventEmitter<T | T[]>();
  @Output() validationChange = new EventEmitter<boolean>();
  @Output() checkChange = new EventEmitter<{ checked: boolean; value: T }>();
  @Output() focusEvent = new EventEmitter<FocusEvent>();
  @Output() blurEvent = new EventEmitter<FocusEvent>();

  @ViewChild('inputRef') inputRef!: ElementRef<HTMLInputElement>;
  @ContentChild(TemplateRef) contentTemplate!: TemplateRef<{
    value: T | T[] | undefined;
  }>;

  modelValue: T | T[] | undefined;
  private touched = false;
  private errors: string[] = [];

  onChange: any = () => {};
  onTouched: any = () => {};

  // Computed classes using signals
  private sizeClasses = signal({
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-7 h-7'
  });

  private variantClasses = signal({
    default: 'border-gray-300 text-blue-600 focus:ring-blue-500',
    primary: 'border-blue-300 text-blue-600 focus:ring-blue-500',
    secondary: 'border-gray-400 text-gray-600 focus:ring-gray-500',
    success: 'border-green-300 text-green-600 focus:ring-green-500',
    warning: 'border-yellow-300 text-yellow-600 focus:ring-yellow-500',
    danger: 'border-red-300 text-red-600 focus:ring-red-500'
  });

  private roundedClasses = signal({
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded',
    lg: 'rounded-lg',
    full: 'rounded-full'
  });

  // Computed class string
  computedClasses = computed(() => {
    const sizeClass = this.sizeClasses()[this.size] || this.sizeClasses()['md'];
    const variantClass = this.variantClasses()[this.variant] || this.variantClasses()['default'];
    const roundedClass = this.roundedClasses()[this.rounded] || this.roundedClasses()['md'];
    const disabledClass = this.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';
    const errorClass = this.hasError() ? 'border-red-500 focus:ring-red-500' : '';
    
    return `${sizeClass} ${variantClass} ${roundedClass} ${disabledClass} ${errorClass} ${this.className}`.trim();
  });

  constructor() {
    // Set default demo values
    if (!this.value && !this.options.length) {
      this.setDefaultValues();
    }
  }

  // Set default values for demo/preview
  private setDefaultValues(): void {
    this.options = [
      { value: 'option1' as T, label: 'Option 1' },
      { value: 'option2' as T, label: 'Option 2' },
      { value: 'option3' as T, label: 'Option 3' }
    ];
    this.label = this.label || 'Select Options';
    this.helpText = this.helpText || 'Choose one or more options';
  }

  writeValue(value: T | T[]): void {
    this.modelValue = value;
    this.validateValue();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    if (this.inputRef) {
      this.inputRef.nativeElement.disabled = isDisabled;
    }
  }

  get el(): HTMLInputElement | undefined {
    return this.inputRef?.nativeElement;
  }

  // Handle value changes
  onValueChange(newValue: T | T[]): void {
    this.modelValue = newValue;
    this.onChange(newValue);
    this.valueChange.emit(newValue);
    
    if (this.validateOnChange) {
      this.validateValue();
    }

    if (typeof newValue === 'boolean' || !Array.isArray(newValue)) {
      this.checkChange.emit({ 
        checked: !!newValue, 
        value: newValue as T 
      });
    }
  }

  // Handle focus events
  onFocus(event: FocusEvent): void {
    this.focusEvent.emit(event);
  }

  // Handle blur events
  onBlur(event: FocusEvent): void {
    this.touched = true;
    this.onTouched();
    this.blurEvent.emit(event);
    this.validateValue();
  }

  // Validation methods
  private validateValue(): void {
    this.errors = [];

    if (this.required) {
      if (Array.isArray(this.modelValue)) {
        if (!this.modelValue.length) {
          this.errors.push('At least one option must be selected');
        }
      } else {
        if (!this.modelValue) {
          this.errors.push('This field is required');
        }
      }
    }

    this.validationChange.emit(this.errors.length === 0);
  }

  // Check if field has error
  hasError(): boolean {
    return this.errors.length > 0 && this.touched;
  }

  // Get validation error message
  getErrorMessage(): string {
    if (this.hasError()) {
      return this.errorMessage || this.errors[0] || '';
    }
    return '';
  }

  // Check if option is selected
  isSelected(option: CheckboxOption<T>): boolean {
    if (Array.isArray(this.modelValue)) {
      return this.modelValue.includes(option.value);
    }
    return this.modelValue === option.value;
  }

  // Toggle option selection
  toggleOption(option: CheckboxOption<T>): void {
    if (option.disabled || this.disabled) {
      return;
    }

    let newValue: T | T[];

    if (this.multiple) {
      const currentArray = Array.isArray(this.modelValue) ? [...this.modelValue] : [];
      const index = currentArray.findIndex(v => v === option.value);
      
      if (index > -1) {
        currentArray.splice(index, 1);
      } else {
        currentArray.push(option.value);
      }
      
      newValue = currentArray;
    } else {
      newValue = this.isSelected(option) ? this.falseValue : option.value;
    }

    this.onValueChange(newValue);
  }

  // Get field classes with validation states
  getFieldClasses(): string {
    const baseClasses = this.computedClasses();
    
    if (this.hasError()) {
      return `${baseClasses} border-red-500 focus:ring-red-500`;
    }
    if (this.touched && !this.hasError()) {
      return `${baseClasses} border-green-500 focus:ring-green-500`;
    }
    
    return baseClasses;
  }

  // Track by function for ngFor
  trackByValue(index: number, option: CheckboxOption<T>): any {
    return option.value;
  }

  // Helper method to handle checkbox change events
  onCheckboxChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const checked = target?.checked || false;
    this.onValueChange(checked ? this.trueValue : this.falseValue);
  }
}