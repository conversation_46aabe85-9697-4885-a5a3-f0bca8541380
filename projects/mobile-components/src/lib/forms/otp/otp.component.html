<div class="otp-container" [class]="className">
  <!-- Label -->
  <label *ngIf="label" class="block text-sm font-medium text-gray-700 mb-2" [class.text-red-600]="hasError()">
    {{ label }}
    <span *ngIf="required" class="text-red-500 ml-1">*</span>
  </label>

  <!-- Help Text -->
  <p *ngIf="helpText" class="text-sm text-gray-500 mb-4">{{ helpText }}</p>

  <!-- OTP Input Fields -->
  <div class="flex items-center justify-center space-x-2 mb-4">
    <ng-container *ngFor="let value of otpValues; let i = index">
      <input
        #otpInput
        type="text"
        maxlength="1"
        [value]="value"
        [placeholder]="placeholder"
        [disabled]="disabled"
        [class]="computedInputClasses() + ' ' + className"
        [attr.aria-label]="'Digit ' + (i + 1) + ' of ' + length"
        [attr.aria-required]="required"
        [attr.aria-invalid]="hasError()"
        [attr.autocomplete]="i === 0 ? 'one-time-code' : 'off'"
        [attr.inputmode]="type === 'number' ? 'numeric' : 'text'"
        [attr.pattern]="type === 'number' ? '[0-9]*' : undefined"
        (input)="onInput($event, i)"
        (keydown)="onKeyDown($event, i)"
        (paste)="onPaste($event, i)"
        (focus)="onFocus(i)"
      />

      <!-- Separator -->
      <span
        *ngIf="showSeparator && separator && i < otpValues.length - 1"
        class="text-gray-400 text-lg font-medium"
      >
        {{ separator }}
      </span>
    </ng-container>
  </div>

  <!-- Actions -->
  <div class="flex items-center justify-between">
    <!-- Clear Button -->
    <button
      *ngIf="hasAnyValue()"
      type="button"
      (click)="clearOtp()"
      [disabled]="disabled"
      class="text-sm text-gray-600 hover:text-gray-800 underline"
    >
      Clear
    </button>
    <div *ngIf="hasNoValue()"></div>

    <!-- Resend Button -->
    <button
      *ngIf="showResendButton && resendEnabled"
      type="button"
      (click)="onResendCode()"
      [disabled]="disabled || resendCountdown > 0"
      class="text-sm font-medium transition-colors"
      [class.text-blue-600]="resendCountdown === 0 && !disabled"
      [class.hover:text-blue-800]="resendCountdown === 0 && !disabled"
      [class.text-gray-400]="resendCountdown > 0 || disabled"
      [class.cursor-not-allowed]="resendCountdown > 0 || disabled"
    >
      {{ getResendButtonText() }}
    </button>
  </div>

  <!-- Error Message -->
  <div *ngIf="hasError() && showValidation" class="mt-2">
    <p class="text-sm text-red-600" role="alert">{{ getErrorMessage() }}</p>
  </div>

  <!-- Progress Indicator -->
  <div *ngIf="hasAnyValue()" class="mt-3">
    <div class="flex space-x-1">
      <div
        *ngFor="let value of otpValues; let i = index"
        class="h-1 flex-1 rounded-full transition-colors"
        [class.bg-blue-500]="value !== ''"
        [class.bg-gray-200]="value === ''"
      ></div>
    </div>
    <p class="text-xs text-gray-500 mt-1 text-center">
      {{ getFilledCount() }} of {{ length }} digits entered
    </p>
  </div>
</div>