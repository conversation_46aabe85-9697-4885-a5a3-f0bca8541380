import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import mobile-optimized components
import { MobileGridComponent } from './mobile-grid/mobile-grid.component';
import { MobileNavigationComponent } from './mobile-navigation/mobile-navigation.component';
import { MobileModalComponent } from './mobile-modal/mobile-modal.component';
import { MobileSwipeCardComponent } from './mobile-swipe-card/mobile-swipe-card.component';

// Import ALL base components
import { AccordianComponent } from './accordian/accordian.component';
import { AutocompleteComponent } from './autocomplete/autocomplete.component';
import { AutocompleteItemComponent } from './autocomplete-item/autocomplete-item.component';
import { AvatarComponent } from './avatar/avatar.component';
import { BreadcrumbComponent } from './breadcrumb/breadcrumb.component';
import { ButtonComponent } from './button/button.component';
import { ButtonActionComponent } from './button-action/button-action.component';
import { ButtonCloseComponent } from './button-close/button-close.component';
import { ButtonIconComponent } from './button-icon/button-icon.component';
import { CardComponent } from './card/card.component';
import { CheckboxComponent } from './checkbox/checkbox.component';
import { CheckboxAnimatedComponent } from './checkbox-animated/checkbox-animated.component';
// import { ContainerComponent } from './container/container.component';
import { DatepickerComponent } from './datepicker/datepicker.component';
import { DividerComponent } from './divider/divider.component';
import { DropdownComponent } from './dropdown/dropdown.component';
import { DropdownDividerComponent } from './dropdown-divider/dropdown-divider.component';
import { DropdownItemComponent } from './dropdown-item/dropdown-item.component';
import { HeadingComponent } from './heading/heading.component';
import { IconComponent } from './icon/icon.component';
import { ImageComponent } from './image/image.component';
import { InputComponent } from './input/input.component';
import { KbdComponent } from './kbd/kbd.component';
import { LinkComponent } from './link/link.component';
import { ListComponent } from './list/list.component';
import { ListboxComponent } from './listbox/listbox.component';
import { LogoComponent } from './logo/logo.component';
import { MessageComponent } from './message/message.component';
import { MobileComponent } from './mobile/mobile.component';
import { ObjectComponent } from './object/object.component';
import { PaginationComponent } from './pagination/pagination.component';
import { ParagraphComponent } from './paragraph/paragraph.component';
import { PictureComponent } from './picture/picture.component';
import { PlaceholderPageComponent } from './placeholder-page/placeholder-page.component';
import { BasePlaceloadComponent } from './placeload/placeload.component';
import { ProgressComponent } from './progress/progress.component';
import { ProseComponent } from './prose/prose.component';
import { RadioComponent } from './radio/radio.component';
import { SelectComponent } from './select/select.component';
import { SnackComponent } from './snack/snack.component';
import { SwitchBallComponent } from './switch-ball/switch-ball.component';
import { SwitchThinComponent } from './switch-thin/switch-thin.component';
import { TableComponent } from './table/table.component';
import { TabsComponent } from './tabs/tabs.component';
import { TagComponent } from './tag/tag.component';
import { TextComponent } from './text/text.component';
import { TextareaComponent } from './textarea/textarea.component';

// All components are standalone in this module
const ALL_COMPONENTS = [
  AccordianComponent,
  AutocompleteComponent,
  AutocompleteItemComponent,
  AvatarComponent,
  BreadcrumbComponent,
  ButtonComponent,
  ButtonActionComponent,
  ButtonCloseComponent,
  ButtonIconComponent,
  CardComponent,
  CheckboxComponent,
  CheckboxAnimatedComponent,
  DatepickerComponent,
  DividerComponent,
  DropdownComponent,
  DropdownDividerComponent,
  DropdownItemComponent,
  HeadingComponent,
  IconComponent,
  ImageComponent,
  InputComponent,
  KbdComponent,
  LinkComponent,
  ListComponent,
  ListboxComponent,
  LogoComponent,
  MessageComponent,
  MobileComponent,
  ObjectComponent,
  PaginationComponent,
  ParagraphComponent,
  PictureComponent,
  PlaceholderPageComponent,
  BasePlaceloadComponent,
  ProgressComponent,
  ProseComponent,
  RadioComponent,
  SelectComponent,
  SnackComponent,
  SwitchBallComponent,
  SwitchThinComponent,
  TableComponent,
  TabsComponent,
  TagComponent,
  TextComponent,
  TextareaComponent,
  MobileGridComponent,
  MobileNavigationComponent,
  MobileModalComponent,
  MobileSwipeCardComponent
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    IonicModule,
    ...ALL_COMPONENTS
  ],
  exports: ALL_COMPONENTS
})
export class BaseModule { }