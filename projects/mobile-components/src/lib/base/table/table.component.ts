import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  type?: 'text' | 'number' | 'date' | 'boolean' | 'custom';
}

export interface TableRow {
  [key: string]: any;
}

@Component({
  selector: 'base-table',
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class TableComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() columns: TableColumn[] = [
    { key: 'name', label: 'Name', sortable: true },
    { key: 'email', label: 'Email', sortable: true },
    { key: 'role', label: 'Role' },
    { key: 'status', label: 'Status' }
  ];
  @Input() data: TableRow[] = [
    { name: 'John Doe', email: '<EMAIL>', role: 'Developer', status: 'Active' },
    { name: 'Jane Smith', email: '<EMAIL>', role: 'Designer', status: 'Active' },
    { name: 'Bob Wilson', email: '<EMAIL>', role: 'Manager', status: 'Inactive' }
  ];
  @Input() loading: boolean = false;
  @Input() striped: boolean = true;
  @Input() hoverable: boolean = true;
  @Input() bordered: boolean = true;
  @Input() compact: boolean = false;
  @Input() sortColumn: string = '';
  @Input() sortDirection: 'asc' | 'desc' = 'asc';
  @Input() selectable: boolean = false;
  @Input() selectedRows: any[] = [];
  @Input() emptyMessage: string = 'No data available';
  @Input() stickyHeader: boolean = false;

  // Events
  @Output() rowClick = new EventEmitter<TableRow>();
  @Output() rowSelect = new EventEmitter<TableRow[]>();
  @Output() sort = new EventEmitter<{ column: string; direction: 'asc' | 'desc' }>();
  @Output() columnClick = new EventEmitter<TableColumn>();

  // Legacy inputs for backward compatibility
  @Input() item_id: string = Math.random().toString(36).substring(7);

  ngOnInit(): void {
    // Component initialization
  }

  get computedClasses(): string {
    const baseClasses = 'w-full border-collapse transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'bg-white',
      primary: 'bg-blue-50',
      secondary: 'bg-gray-50',
      success: 'bg-green-50',
      warning: 'bg-yellow-50',
      danger: 'bg-red-50'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const stateClasses = [];
    if (this.bordered) stateClasses.push('border border-gray-200');
    if (this.compact) stateClasses.push('table-auto');
    else stateClasses.push('table-fixed');

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      ...stateClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get tableWrapperClasses(): string {
    const baseClasses = 'overflow-x-auto';
    const stickyClasses = this.stickyHeader ? 'max-h-96 overflow-y-auto' : '';
    const roundedClasses = this.rounded !== 'none' ? `rounded-${this.rounded}` : '';
    
    return [baseClasses, stickyClasses, roundedClasses].filter(Boolean).join(' ');
  }

  get headerRowClasses(): string {
    const baseClasses = 'bg-gray-50 border-b border-gray-200';
    const stickyClasses = this.stickyHeader ? 'sticky top-0 z-10' : '';
    
    return [baseClasses, stickyClasses].filter(Boolean).join(' ');
  }

  getRowClasses(row: TableRow, index: number): string {
    const baseClasses = 'border-b border-gray-100';
    const stripedClasses = this.striped && index % 2 === 1 ? 'bg-gray-50' : '';
    const hoverClasses = this.hoverable ? 'hover:bg-gray-100 transition-colors duration-150' : '';
    const selectableClasses = this.selectable ? 'cursor-pointer' : '';
    const selectedClasses = this.isRowSelected(row) ? 'bg-blue-50 border-blue-200' : '';
    
    return [baseClasses, stripedClasses, hoverClasses, selectableClasses, selectedClasses]
      .filter(Boolean)
      .join(' ');
  }

  getCellClasses(column: TableColumn): string {
    const baseClasses = 'px-4 py-3';
    const alignClasses = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right'
    };
    
    return [baseClasses, alignClasses[column.align || 'left']].filter(Boolean).join(' ');
  }

  getHeaderCellClasses(column: TableColumn): string {
    const baseClasses = 'px-4 py-3 font-medium text-gray-900';
    const sortableClasses = column.sortable ? 'cursor-pointer hover:bg-gray-100 select-none' : '';
    const alignClasses = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right'
    };
    
    return [baseClasses, sortableClasses, alignClasses[column.align || 'left']]
      .filter(Boolean)
      .join(' ');
  }

  onRowClick(row: TableRow): void {
    if (this.selectable) {
      this.toggleRowSelection(row);
    }
    this.rowClick.emit(row);
  }

  onHeaderClick(column: TableColumn): void {
    if (column.sortable) {
      const newDirection = 
        this.sortColumn === column.key && this.sortDirection === 'asc' ? 'desc' : 'asc';
      this.sortColumn = column.key;
      this.sortDirection = newDirection;
      this.sort.emit({ column: column.key, direction: newDirection });
    }
    this.columnClick.emit(column);
  }

  private toggleRowSelection(row: TableRow): void {
    const index = this.selectedRows.findIndex(selected => 
      JSON.stringify(selected) === JSON.stringify(row)
    );
    
    if (index > -1) {
      this.selectedRows.splice(index, 1);
    } else {
      this.selectedRows.push(row);
    }
    
    this.rowSelect.emit([...this.selectedRows]);
  }

  private isRowSelected(row: TableRow): boolean {
    return this.selectedRows.some(selected => 
      JSON.stringify(selected) === JSON.stringify(row)
    );
  }

  getSortIcon(column: TableColumn): string {
    if (!column.sortable || this.sortColumn !== column.key) {
      return '';
    }
    return this.sortDirection === 'asc' ? '↑' : '↓';
  }

  getCellValue(row: TableRow, column: TableColumn): any {
    return row[column.key];
  }

  trackByColumn(index: number, column: TableColumn): string {
    return column.key;
  }

  trackByRow(index: number, row: TableRow): any {
    return row['id'] || index;
  }
}
