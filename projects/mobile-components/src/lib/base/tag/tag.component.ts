import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
  OnInit,
  OnChanges,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-tag',
  templateUrl: './tag.component.html',
  styleUrls: ['./tag.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class TagComponent implements OnInit, OnChanges {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'solid' | 'outline' | 'pastel' = 'solid';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'lg';

  // Component-specific inputs
  @Input() text: string = 'Sample Tag';
  @Input() removable: boolean = false;
  @Input() icon?: string;
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() clickable: boolean = false;
  @Input() disabled: boolean = false;

  /**
   * The legacy variant of the tag.
   *
   * @since 2.0.0
   * @default 'solid'
   */
  @Input() legacyVariant: 'solid' | 'outline' | 'pastel' = 'solid';

  /**
   * The color of the tag.
   *
   * @default 'default'
   */
  @Input() color:
    | 'default'
    | 'default-contrast'
    | 'muted'
    | 'muted-contrast'
    | 'light'
    | 'dark'
    | 'black'
    | 'primary'
    | 'info'
    | 'success'
    | 'secondary'
    | 'warning'
    | 'danger' = 'default';


  /**
   * The legacy size of the tag.
   *
   * @default 'md'
   */
  @Input() legacySize: 'sm' | 'md' = 'md';

  /**
   * Determines when the tag should have a shadow.
   */
  @Input() shadow?: 'flat' | 'hover';

  @Input() item_id: string = Math.random().toString(36).substring(7);

  // Events
  @Output() tagClick = new EventEmitter<Event>();
  @Output() remove = new EventEmitter<Event>();

  classes: string = '';

  private variants: Record<string, string> = {
    solid: 'nui-tag-solid',
    pastel: 'nui-tag-pastel',
    outline: 'nui-tag-outline',
  };

  private radiuses: Record<string, string> = {
    none: '',
    sm: 'nui-tag-rounded-sm',
    md: 'nui-tag-rounded-md',
    lg: 'nui-tag-rounded-lg',
    full: 'nui-tag-rounded-full',
  };

  private colors: Record<string, string> = {
    default: 'nui-tag-default',
    'default-contrast': 'nui-tag-default-contrast',
    muted: 'nui-tag-muted',
    'muted-contrast': 'nui-tag-muted-contrast',
    light: 'nui-tag-light',
    dark: 'nui-tag-dark',
    black: 'nui-tag-black',
    primary: 'nui-tag-primary',
    info: 'nui-tag-info',
    success: 'nui-tag-success',
    warning: 'nui-tag-warning',
    danger: 'nui-tag-danger',
    secondary: 'nui-tag-secondary',
  };

  private sizes: Record<string, string> = {
    sm: 'nui-tag-sm',
    md: 'nui-tag-md',
  };

  private shadows: Record<string, string> = {
    flat: 'nui-tag-shadow',
    hover: 'nui-tag-shadow-hover',
  };

  ngOnInit(): void {
    this.computeClasses();
  }

  ngOnChanges(): void {
    this.computeClasses();
  }

  get computedClasses(): string {
    const baseClasses = 'inline-flex items-center gap-1 font-medium transition-all duration-200';
    
    const sizeClasses = {
      xs: 'px-1.5 py-0.5 text-xs',
      sm: 'px-2 py-1 text-xs',
      md: 'px-2.5 py-1 text-sm',
      lg: 'px-3 py-1.5 text-sm',
      xl: 'px-4 py-2 text-base'
    };

    const variantClasses = {
      default: 'bg-gray-100 text-gray-800 border border-gray-200',
      primary: 'bg-blue-100 text-blue-800 border border-blue-200',
      secondary: 'bg-gray-100 text-gray-700 border border-gray-300',
      success: 'bg-green-100 text-green-800 border border-green-200',
      warning: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
      danger: 'bg-red-100 text-red-800 border border-red-200',
      solid: 'bg-blue-600 text-white',
      outline: 'bg-transparent border border-blue-600 text-blue-600',
      pastel: 'bg-blue-50 text-blue-700 border border-blue-100'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const stateClasses = [];
    if (this.disabled) stateClasses.push('opacity-50 cursor-not-allowed');
    if (this.clickable && !this.disabled) stateClasses.push('cursor-pointer hover:opacity-80');
    if (this.removable) stateClasses.push('pr-1');

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      ...stateClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  private computeClasses(): void {
    // Legacy method for backward compatibility
    const legacyVariant = this.legacyVariant || (this.variant as any);
    const legacySize = this.legacySize || 'md';
    
    this.classes = [
      'nui-tag',
      legacySize ? this.sizes[legacySize] : '',
      this.rounded ? this.radiuses[this.rounded] : '',
      legacyVariant ? this.variants[legacyVariant] : '',
      this.color ? this.colors[this.color] : '',
      this.shadow ? this.shadows[this.shadow] : '',
    ]
      .filter(Boolean)
      .join(' ');
  }

  onClick(event: Event): void {
    if (!this.disabled && this.clickable) {
      this.tagClick.emit(event);
    }
  }

  onRemove(event: Event): void {
    if (!this.disabled) {
      event.stopPropagation();
      this.remove.emit(event);
    }
  }
}
