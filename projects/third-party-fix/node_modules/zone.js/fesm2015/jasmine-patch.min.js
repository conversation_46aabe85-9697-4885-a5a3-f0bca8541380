"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */function patchJasmine(e){e.__load_patch("jasmine",((e,n,t)=>{if(!n)throw new Error("Missing: zone.js");if("undefined"!=typeof jest)return;if("undefined"==typeof jasmine||jasmine.__zone_patch__)return;jasmine.__zone_patch__=!0;const o=n.SyncTestZoneSpec,s=n.ProxyZoneSpec;if(!o)throw new Error("Missing: SyncTestZoneSpec");if(!s)throw new Error("Missing: ProxyZoneSpec");const r=n.current,c=n.__symbol__,i=!0===e[c("fakeAsyncDisablePatchingClock")],a=!i&&(!0===e[c("fakeAsyncPatchLock")]||!0===e[c("fakeAsyncAutoFakeAsyncWhenClockPatched")]);if(!0!==e[c("ignoreUnhandledRejection")]){const n=jasmine.GlobalErrors;n&&!jasmine[c("GlobalErrors")]&&(jasmine[c("GlobalErrors")]=n,jasmine.GlobalErrors=function(){const t=new n,o=t.install;return o&&!t[c("install")]&&(t[c("install")]=o,t.install=function(){const n="undefined"!=typeof process&&!!process.on,t=n?process.listeners("unhandledRejection"):e.eventListeners("unhandledrejection"),s=o.apply(this,arguments);return n?process.removeAllListeners("unhandledRejection"):e.removeAllListeners("unhandledrejection"),t&&t.forEach((t=>{n?process.on("unhandledRejection",t):e.addEventListener("unhandledrejection",t)})),s}),t})}const l=jasmine.getEnv();if(["describe","xdescribe","fdescribe"].forEach((e=>{let n=l[e];l[e]=function(e,t){return n.call(this,e,function s(e,n){return function(){return r.fork(new o(`jasmine.describe#${e}`)).run(n,this,arguments)}}(e,t))}})),["it","xit","fit"].forEach((e=>{let n=l[e];l[c(e)]=n,l[e]=function(e,t,o){return arguments[1]=p(t),n.apply(this,arguments)}})),["beforeEach","afterEach","beforeAll","afterAll"].forEach((e=>{let n=l[e];l[c(e)]=n,l[e]=function(e,t){return arguments[0]=p(e),n.apply(this,arguments)}})),!i){const e=jasmine[c("clock")]=jasmine.clock;jasmine.clock=function(){const t=e.apply(this,arguments);if(!t[c("patched")]){t[c("patched")]=c("patched");const e=t[c("tick")]=t.tick;t.tick=function(){const t=n.current.get("FakeAsyncTestZoneSpec");return t?t.tick.apply(t,arguments):e.apply(this,arguments)};const o=t[c("mockDate")]=t.mockDate;t.mockDate=function(){const e=n.current.get("FakeAsyncTestZoneSpec");if(e){const n=arguments.length>0?arguments[0]:new Date;return e.setFakeBaseSystemTime.apply(e,n&&"function"==typeof n.getTime?[n.getTime()]:arguments)}return o.apply(this,arguments)},a&&["install","uninstall"].forEach((e=>{const o=t[c(e)]=t[e];t[e]=function(){if(!n.FakeAsyncTestZoneSpec)return o.apply(this,arguments);jasmine[c("clockInstalled")]="install"===e}}))}return t}}if(!jasmine[n.__symbol__("createSpyObj")]){const e=jasmine.createSpyObj;jasmine[n.__symbol__("createSpyObj")]=e,jasmine.createSpyObj=function(){const n=Array.prototype.slice.call(arguments);let t;if(n.length>=3&&n[2]){const o=Object.defineProperty;Object.defineProperty=function(e,n,t){return o.call(this,e,n,{...t,configurable:!0,enumerable:!0})};try{t=e.apply(this,n)}finally{Object.defineProperty=o}}else t=e.apply(this,n);return t}}function u(e,t,o,s){const r=!!jasmine[c("clockInstalled")],i=o.testProxyZone;if(r&&a){const t=n[n.__symbol__("fakeAsyncTest")];t&&"function"==typeof t.fakeAsync&&(e=t.fakeAsync(e))}return s?i.run(e,t,[s]):i.run(e,t)}function p(e){return e&&(e.length?function(n){return u(e,this,this.queueRunner,n)}:function(){return u(e,this,this.queueRunner)})}const f=jasmine.QueueRunner;jasmine.QueueRunner=function(t){function o(o){o.onComplete&&(o.onComplete=(e=>()=>{this.testProxyZone=null,this.testProxyZoneSpec=null,r.scheduleMicroTask("jasmine.onComplete",e)})(o.onComplete));const s=e[n.__symbol__("setTimeout")],c=e[n.__symbol__("clearTimeout")];s&&(o.timeout={setTimeout:s||e.setTimeout,clearTimeout:c||e.clearTimeout}),jasmine.UserContext?(o.userContext||(o.userContext=new jasmine.UserContext),o.userContext.queueRunner=this):(o.userContext||(o.userContext={}),o.userContext.queueRunner=this);const i=o.onException;o.onException=function(e){if(e&&"Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL."===e.message){const n=this&&this.testProxyZoneSpec;if(n){const t=n.getAndClearPendingTasksInfo();try{e.message+=t}catch(e){}}}i&&i.call(this,e)},t.call(this,o)}return function(e,n){for(const t in n)n.hasOwnProperty(t)&&(e[t]=n[t]);function t(){this.constructor=e}e.prototype=null===n?Object.create(n):(t.prototype=n.prototype,new t)}(o,t),o.prototype.execute=function(){let e=n.current,o=!1;for(;e;){if(e===r){o=!0;break}e=e.parent}if(!o)throw new Error("Unexpected Zone: "+n.current.name);this.testProxyZoneSpec=new s,this.testProxyZone=r.fork(this.testProxyZoneSpec),n.currentTask?t.prototype.execute.call(this):n.current.scheduleMicroTask("jasmine.execute().forceTask",(()=>f.prototype.execute.call(this)))},o}(f)}))}patchJasmine(Zone);