"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){var e=function(){function e(){this.name="TaskTrackingZone",this.microTasks=[],this.macroTasks=[],this.eventTasks=[],this.properties={TaskTrackingZone:this}}return e.get=function(){return Zone.current.get("TaskTrackingZone")},e.prototype.getTasksFor=function(e){switch(e){case"microTask":return this.microTasks;case"macroTask":return this.macroTasks;case"eventTask":return this.eventTasks}throw new Error("Unknown task format: "+e)},e.prototype.onScheduleTask=function(e,t,n,s){return s.creationLocation=new Error("Task '".concat(s.type,"' from '").concat(s.source,"'.")),this.getTasksFor(s.type).push(s),e.scheduleTask(n,s)},e.prototype.onCancelTask=function(e,t,n,s){for(var r=this.getTasksFor(s.type),o=0;o<r.length;o++)if(r[o]==s){r.splice(o,1);break}return e.cancelTask(n,s)},e.prototype.onInvokeTask=function(e,t,n,s,r,o){var a;if("eventTask"===s.type||(null===(a=s.data)||void 0===a?void 0:a.isPeriodic))return e.invokeTask(n,s,r,o);for(var i=this.getTasksFor(s.type),c=0;c<i.length;c++)if(i[c]==s){i.splice(c,1);break}return e.invokeTask(n,s,r,o)},e.prototype.clearEvents=function(){for(;this.eventTasks.length;)Zone.current.cancelTask(this.eventTasks[0])},e}();!function t(n){n.TaskTrackingZoneSpec=e}(Zone)}));