"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},__assign.apply(this,arguments)};
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){var e=globalThis;function t(t){return(e.__Zone_symbol_prefix||"__zone_symbol__")+t}function n(){var n,r=e.performance;function o(e){r&&r.mark&&r.mark(e)}function a(e,t){r&&r.measure&&r.measure(e,t)}o("Zone");var i=function(){function r(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new u(this,this._parent&&this._parent._zoneDelegate,t)}return r.assertZonePatched=function(){if(e.Promise!==Z.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")},Object.defineProperty(r,"root",{get:function(){for(var e=n.current;e.parent;)e=e.parent;return e},enumerable:!1,configurable:!0}),Object.defineProperty(r,"current",{get:function(){return C.zone},enumerable:!1,configurable:!0}),Object.defineProperty(r,"currentTask",{get:function(){return z},enumerable:!1,configurable:!0}),r.__load_patch=function(r,i,c){if(void 0===c&&(c=!1),Z.hasOwnProperty(r)){var s=!0===e[t("forceDuplicateZoneCheck")];if(!c&&s)throw Error("Already loaded patch: "+r)}else if(!e["__Zone_disable_"+r]){var u="Zone:"+r;o(u),Z[r]=i(e,n,j),a(u,u)}},Object.defineProperty(r.prototype,"parent",{get:function(){return this._parent},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),r.prototype.get=function(e){var t=this.getZoneWith(e);if(t)return t._properties[e]},r.prototype.getZoneWith=function(e){for(var t=this;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null},r.prototype.fork=function(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)},r.prototype.wrap=function(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);var n=this._zoneDelegate.intercept(this,e,t),r=this;return function(){return r.runGuarded(n,this,arguments,t)}},r.prototype.run=function(e,t,n,r){C={parent:C,zone:this};try{return this._zoneDelegate.invoke(this,e,t,n,r)}finally{C=C.parent}},r.prototype.runGuarded=function(e,t,n,r){void 0===t&&(t=null),C={parent:C,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,n,r)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{C=C.parent}},r.prototype.runTask=function(e,t,n){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||k).name+"; Execution: "+this.name+")");var r=e,o=e.type,a=e.data,i=void 0===a?{}:a,c=i.isPeriodic,s=void 0!==c&&c,u=i.isRefreshable,l=void 0!==u&&u;if(e.state!==m||o!==D&&o!==O){var f=e.state!=E;f&&r._transitionTo(E,T);var h=z;z=r,C={parent:C,zone:this};try{o!=O||!e.data||s||l||(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,r,t,n)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{var p=e.state;if(p!==m&&p!==S)if(o==D||s||l&&p===b)f&&r._transitionTo(T,E,b);else{var v=r._zoneDelegates;this._updateTaskCount(r,-1),f&&r._transitionTo(m,E,m),l&&(r._zoneDelegates=v)}C=C.parent,z=h}}},r.prototype.scheduleTask=function(e){if(e.zone&&e.zone!==this)for(var t=this;t;){if(t===e.zone)throw Error("can not reschedule task to ".concat(this.name," which is descendants of the original zone ").concat(e.zone.name));t=t.parent}e._transitionTo(b,m);var n=[];e._zoneDelegates=n,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(t){throw e._transitionTo(S,b,m),this._zoneDelegate.handleError(this,t),t}return e._zoneDelegates===n&&this._updateTaskCount(e,1),e.state==b&&e._transitionTo(T,b),e},r.prototype.scheduleMicroTask=function(e,t,n,r){return this.scheduleTask(new l(P,e,t,n,r,void 0))},r.prototype.scheduleMacroTask=function(e,t,n,r,o){return this.scheduleTask(new l(O,e,t,n,r,o))},r.prototype.scheduleEventTask=function(e,t,n,r,o){return this.scheduleTask(new l(D,e,t,n,r,o))},r.prototype.cancelTask=function(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||k).name+"; Execution: "+this.name+")");if(e.state===T||e.state===E){e._transitionTo(w,T,E);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(S,w),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(m,w),e.runCount=-1,e}},r.prototype._updateTaskCount=function(e,t){var n=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(var r=0;r<n.length;r++)n[r]._updateTaskCount(e.type,t)},r}();(n=i).__symbol__=t;var c,s={name:"",onHasTask:function(e,t,n,r){return e.hasTask(n,r)},onScheduleTask:function(e,t,n,r){return e.scheduleTask(n,r)},onInvokeTask:function(e,t,n,r,o,a){return e.invokeTask(n,r,o,a)},onCancelTask:function(e,t,n,r){return e.cancelTask(n,r)}},u=function(){function e(e,t,n){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this._zone=e,this._parentDelegate=t,this._forkZS=n&&(n&&n.onFork?n:t._forkZS),this._forkDlgt=n&&(n.onFork?t:t._forkDlgt),this._forkCurrZone=n&&(n.onFork?this._zone:t._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:t._interceptZS),this._interceptDlgt=n&&(n.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this._zone:t._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:t._invokeZS),this._invokeDlgt=n&&(n.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this._zone:t._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:t._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this._zone:t._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:t._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this._zone:t._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:t._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this._zone:t._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:t._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this._zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;var r=n&&n.onHasTask;(r||t&&t._hasTaskZS)&&(this._hasTaskZS=r?n:s,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,n.onScheduleTask||(this._scheduleTaskZS=s,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this._zone),n.onInvokeTask||(this._invokeTaskZS=s,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this._zone),n.onCancelTask||(this._cancelTaskZS=s,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this._zone))}return Object.defineProperty(e.prototype,"zone",{get:function(){return this._zone},enumerable:!1,configurable:!0}),e.prototype.fork=function(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new i(e,t)},e.prototype.intercept=function(e,t,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,n):t},e.prototype.invoke=function(e,t,n,r,o){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,n,r,o):t.apply(n,r)},e.prototype.handleError=function(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)},e.prototype.scheduleTask=function(e,t){var n=t;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),(n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t))||(n=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=P)throw new Error("Task is missing scheduleFn.");g(t)}return n},e.prototype.invokeTask=function(e,t,n,r){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,n,r):t.callback.apply(n,r)},e.prototype.cancelTask=function(e,t){var n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");n=t.cancelFn(t)}return n},e.prototype.hasTask=function(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(t){this.handleError(e,t)}},e.prototype._updateTaskCount=function(e,t){var n=this._taskCounts,r=n[e],o=n[e]=r+t;if(o<0)throw new Error("More tasks executed then were scheduled.");0!=r&&0!=o||this.hasTask(this._zone,{microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:e})},e}(),l=function(){function t(n,r,o,a,i,c){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=n,this.source=r,this.data=a,this.scheduleFn=i,this.cancelFn=c,!o)throw new Error("callback is not defined");this.callback=o;var s=this;this.invoke=n===D&&a&&a.useG?t.invokeTask:function(){return t.invokeTask.call(e,s,this,arguments)}}return t.invokeTask=function(e,t,n){e||(e=this),I++;try{return e.runCount++,e.zone.runTask(e,t,n)}finally{1==I&&y(),I--}},Object.defineProperty(t.prototype,"zone",{get:function(){return this._zone},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),t.prototype.cancelScheduleRequest=function(){this._transitionTo(m,b)},t.prototype._transitionTo=function(e,t,n){if(this._state!==t&&this._state!==n)throw new Error("".concat(this.type," '").concat(this.source,"': can not transition to '").concat(e,"', expecting state '").concat(t,"'").concat(n?" or '"+n+"'":"",", was '").concat(this._state,"'."));this._state=e,e==m&&(this._zoneDelegates=null)},t.prototype.toString=function(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)},t.prototype.toJSON=function(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}},t}(),f=t("setTimeout"),h=t("Promise"),p=t("then"),v=[],d=!1;function _(t){if(c||e[h]&&(c=e[h].resolve(0)),c){var n=c[p];n||(n=c.then),n.call(c,t)}else e[f](t,0)}function g(e){0===I&&0===v.length&&_(y),e&&v.push(e)}function y(){if(!d){for(d=!0;v.length;){var e=v;v=[];for(var t=0;t<e.length;t++){var n=e[t];try{n.zone.runTask(n,null,null)}catch(e){j.onUnhandledError(e)}}}j.microtaskDrainDone(),d=!1}}var k={name:"NO ZONE"},m="notScheduled",b="scheduling",T="scheduled",E="running",w="canceling",S="unknown",P="microTask",O="macroTask",D="eventTask",Z={},j={symbol:t,currentZoneFrame:function(){return C},onUnhandledError:R,microtaskDrainDone:R,scheduleMicroTask:g,showUncaughtError:function(){return!i[t("ignoreConsoleErrorUncaughtError")]},patchEventTarget:function(){return[]},patchOnProperties:R,patchMethod:function(){return R},bindArguments:function(){return[]},patchThen:function(){return R},patchMacroTask:function(){return R},patchEventPrototype:function(){return R},isIEOrEdge:function(){return!1},getGlobalObjects:function(){},ObjectDefineProperty:function(){return R},ObjectGetOwnPropertyDescriptor:function(){},ObjectCreate:function(){},ArraySlice:function(){return[]},patchClass:function(){return R},wrapWithCurrentZone:function(){return R},filterProperties:function(){return[]},attachOriginToPatched:function(){return R},_redefineProperty:function(){return R},patchCallbacks:function(){return R},nativeScheduleMicroTask:_},C={parent:null,zone:new i(null,null)},z=null,I=0;function R(){}return a("Zone","Zone"),i}var r=Object.getOwnPropertyDescriptor,o=Object.defineProperty,a=Object.getPrototypeOf,i=Object.create,c=Array.prototype.slice,s="addEventListener",u="removeEventListener",l=t(s),f=t(u),h="true",p="false",v=t("");function d(e,t){return Zone.current.wrap(e,t)}function _(e,t,n,r,o){return Zone.current.scheduleMacroTask(e,t,n,r,o)}var g=t,y="undefined"!=typeof window,k=y?window:void 0,m=y&&k||globalThis,b="removeAttribute";function T(e,t){for(var n=e.length-1;n>=0;n--)"function"==typeof e[n]&&(e[n]=d(e[n],t+"_"+n));return e}function E(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}var w="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,S=!("nw"in m)&&void 0!==m.process&&"[object process]"===m.process.toString(),P=!S&&!w&&!(!y||!k.HTMLElement),O=void 0!==m.process&&"[object process]"===m.process.toString()&&!w&&!(!y||!k.HTMLElement),D={},Z=g("enable_beforeunload"),j=function(e){if(e=e||m.event){var t=D[e.type];t||(t=D[e.type]=g("ON_PROPERTY"+e.type));var n,r=this||e.target||m,o=r[t];return P&&r===k&&"error"===e.type?!0===(n=o&&o.call(this,e.message,e.filename,e.lineno,e.colno,e.error))&&e.preventDefault():(n=o&&o.apply(this,arguments),"beforeunload"===e.type&&m[Z]&&"string"==typeof n?e.returnValue=n:null==n||n||e.preventDefault()),n}};function C(e,t,n){var a=r(e,t);if(!a&&n&&r(n,t)&&(a={enumerable:!0,configurable:!0}),a&&a.configurable){var i=g("on"+t+"patched");if(!e.hasOwnProperty(i)||!e[i]){delete a.writable,delete a.value;var c=a.get,s=a.set,u=t.slice(2),l=D[u];l||(l=D[u]=g("ON_PROPERTY"+u)),a.set=function(t){var n=this;n||e!==m||(n=m),n&&("function"==typeof n[l]&&n.removeEventListener(u,j),s&&s.call(n,null),n[l]=t,"function"==typeof t&&n.addEventListener(u,j,!1))},a.get=function(){var n=this;if(n||e!==m||(n=m),!n)return null;var r=n[l];if(r)return r;if(c){var o=c.call(this);if(o)return a.set.call(this,o),"function"==typeof n[b]&&n.removeAttribute(t),o}return null},o(e,t,a),e[i]=!0}}}function z(e,t,n){if(t)for(var r=0;r<t.length;r++)C(e,"on"+t[r],n);else{var o=[];for(var a in e)"on"==a.slice(0,2)&&o.push(a);for(var i=0;i<o.length;i++)C(e,o[i],n)}}var I=g("originalInstance");function R(e){var t=m[e];if(t){m[g(e)]=t,m[e]=function(){var n=T(arguments,e);switch(n.length){case 0:this[I]=new t;break;case 1:this[I]=new t(n[0]);break;case 2:this[I]=new t(n[0],n[1]);break;case 3:this[I]=new t(n[0],n[1],n[2]);break;case 4:this[I]=new t(n[0],n[1],n[2],n[3]);break;default:throw new Error("Arg list too long.")}},x(m[e],t);var n,r=new t((function(){}));for(n in r)"XMLHttpRequest"===e&&"responseBlob"===n||function(t){"function"==typeof r[t]?m[e].prototype[t]=function(){return this[I][t].apply(this[I],arguments)}:o(m[e].prototype,t,{set:function(n){"function"==typeof n?(this[I][t]=d(n,e+"."+t),x(this[I][t],n)):this[I][t]=n},get:function(){return this[I][t]}})}(n);for(n in t)"prototype"!==n&&t.hasOwnProperty(n)&&(m[e][n]=t[n])}}var M=!1;function A(e,t,n){for(var o=e;o&&!o.hasOwnProperty(t);)o=a(o);!o&&e[t]&&(o=e);var i=g(t),c=null;if(o&&(!(c=o[i])||!o.hasOwnProperty(i))&&(c=o[i]=o[t],E(o&&r(o,t)))){var s=n(c,i,t);o[t]=function(){return s(this,arguments)},x(o[t],c),M&&function e(t,n){"function"==typeof Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(t).forEach((function(e){var r=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(n,e,{get:function(){return t[e]},set:function(n){(!r||r.writable&&"function"==typeof r.set)&&(t[e]=n)},enumerable:!r||r.enumerable,configurable:!r||r.configurable})}))}(c,o[t])}return c}function L(e,t,n){var r=null;function o(e){var t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},r.apply(t.target,t.args),e}r=A(e,t,(function(e){return function(t,r){var a=n(t,r);return a.cbIdx>=0&&"function"==typeof r[a.cbIdx]?_(a.name,r[a.cbIdx],a,o):e.apply(t,r)}}))}function N(e,t,n){var r=null;function o(e){var t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},r.apply(t.target,t.args),e}r=A(e,t,(function(e){return function(t,r){var a=n(t,r);return a.cbIdx>=0&&"function"==typeof r[a.cbIdx]?Zone.current.scheduleMicroTask(a.name,r[a.cbIdx],a,o):e.apply(t,r)}}))}function x(e,t){e[g("OriginalDelegate")]=t}var H=!1,F=!1;function q(){if(H)return F;H=!0;try{var e=k.navigator.userAgent;-1===e.indexOf("MSIE ")&&-1===e.indexOf("Trident/")&&-1===e.indexOf("Edge/")||(F=!0)}catch(e){}return F}function G(e){return"function"==typeof e}function B(e){return"number"==typeof e}var U=!1;if("undefined"!=typeof window)try{var W=Object.defineProperty({},"passive",{get:function(){U=!0}});window.addEventListener("test",W,W),window.removeEventListener("test",W,W)}catch(e){U=!1}var V={useG:!0},X={},Y={},J=new RegExp("^"+v+"(\\w+)(true|false)$"),K=g("propagationStopped");function $(e,t){var n=(t?t(e):e)+p,r=(t?t(e):e)+h,o=v+n,a=v+r;X[e]={},X[e][p]=o,X[e][h]=a}function Q(e,t,n,r){var o=r&&r.add||s,i=r&&r.rm||u,c=r&&r.listeners||"eventListeners",l=r&&r.rmAll||"removeAllListeners",f=g(o),d="."+o+":",_="prependListener",y="."+_+":",k=function(e,t,n){if(!e.isRemoved){var r,o=e.callback;"object"==typeof o&&o.handleEvent&&(e.callback=function(e){return o.handleEvent(e)},e.originalDelegate=o);try{e.invoke(e,t,[n])}catch(e){r=e}var a=e.options;return a&&"object"==typeof a&&a.once&&t[i].call(t,n.type,e.originalDelegate?e.originalDelegate:e.callback,a),r}};function m(n,r,o){if(r=r||e.event){var a=n||r.target||e,i=a[X[r.type][o?h:p]];if(i){var c=[];if(1===i.length)(l=k(i[0],a,r))&&c.push(l);else for(var s=i.slice(),u=0;u<s.length&&(!r||!0!==r[K]);u++){var l;(l=k(s[u],a,r))&&c.push(l)}if(1===c.length)throw c[0];var f=function(e){var n=c[e];t.nativeScheduleMicroTask((function(){throw n}))};for(u=0;u<c.length;u++)f(u)}}}var b=function(e){return m(this,e,!1)},T=function(e){return m(this,e,!0)};function E(t,n){if(!t)return!1;var r=!0;n&&void 0!==n.useG&&(r=n.useG);var s=n&&n.vh,u=!0;n&&void 0!==n.chkDup&&(u=n.chkDup);var k=!1;n&&void 0!==n.rt&&(k=n.rt);for(var m=t;m&&!m.hasOwnProperty(o);)m=a(m);if(!m&&t[o]&&(m=t),!m)return!1;if(m[f])return!1;var E,w=n&&n.eventNameToString,P={},O=m[f]=m[o],D=m[g(i)]=m[i],Z=m[g(c)]=m[c],j=m[g(l)]=m[l];n&&n.prepend&&(E=m[g(n.prepend)]=m[n.prepend]);var C=r?function(e){if(!P.isExisting)return O.call(P.target,P.eventName,P.capture?T:b,P.options)}:function(e){return O.call(P.target,P.eventName,e.invoke,P.options)},z=r?function(e){if(!e.isRemoved){var t=X[e.eventName],n=void 0;t&&(n=t[e.capture?h:p]);var r=n&&e.target[n];if(r)for(var o=0;o<r.length;o++)if(r[o]===e){r.splice(o,1),e.isRemoved=!0,e.removeAbortListener&&(e.removeAbortListener(),e.removeAbortListener=null),0===r.length&&(e.allRemoved=!0,e.target[n]=null);break}}if(e.allRemoved)return D.call(e.target,e.eventName,e.capture?T:b,e.options)}:function(e){return D.call(e.target,e.eventName,e.invoke,e.options)},I=n&&n.diff?n.diff:function(e,t){var n=typeof t;return"function"===n&&e.callback===t||"object"===n&&e.originalDelegate===t},R=Zone[g("UNPATCHED_EVENTS")],M=e[g("PASSIVE_EVENTS")],A=function(t,o,a,i,c,l){return void 0===c&&(c=!1),void 0===l&&(l=!1),function(){var f=this||e,v=arguments[0];n&&n.transferEventName&&(v=n.transferEventName(v));var d=arguments[1];if(!d)return t.apply(this,arguments);if(S&&"uncaughtException"===v)return t.apply(this,arguments);var _=!1;if("function"!=typeof d){if(!d.handleEvent)return t.apply(this,arguments);_=!0}if(!s||s(t,d,f,arguments)){var g=U&&!!M&&-1!==M.indexOf(v),y=function n(e){if("object"==typeof e&&null!==e){var t=__assign({},e);return e.signal&&(t.signal=e.signal),t}return e}(function e(t,n){return!U&&"object"==typeof t&&t?!!t.capture:U&&n?"boolean"==typeof t?{capture:t,passive:!0}:t?"object"==typeof t&&!1!==t.passive?__assign(__assign({},t),{passive:!0}):t:{passive:!0}:t}(arguments[2],g)),k=null==y?void 0:y.signal;if(!(null==k?void 0:k.aborted)){if(R)for(var m=0;m<R.length;m++)if(v===R[m])return g?t.call(f,v,d,y):t.apply(this,arguments);var b=!!y&&("boolean"==typeof y||y.capture),T=!(!y||"object"!=typeof y)&&y.once,E=Zone.current,O=X[v];O||($(v,w),O=X[v]);var D,Z=O[b?h:p],j=f[Z],C=!1;if(j){if(C=!0,u)for(m=0;m<j.length;m++)if(I(j[m],d))return}else j=f[Z]=[];var z=f.constructor.name,A=Y[z];A&&(D=A[v]),D||(D=z+o+(w?w(v):v)),P.options=y,T&&(P.options.once=!1),P.target=f,P.capture=b,P.eventName=v,P.isExisting=C;var L=r?V:void 0;L&&(L.taskData=P),k&&(P.options.signal=void 0);var N=E.scheduleEventTask(D,d,L,a,i);if(k){P.options.signal=k;var x=function(){return N.zone.cancelTask(N)};t.call(k,"abort",x,{once:!0}),N.removeAbortListener=function(){return k.removeEventListener("abort",x)}}return P.target=null,L&&(L.taskData=null),T&&(P.options.once=!0),(U||"boolean"!=typeof N.options)&&(N.options=y),N.target=f,N.capture=b,N.eventName=v,_&&(N.originalDelegate=d),l?j.unshift(N):j.push(N),c?f:void 0}}}};return m[o]=A(O,d,C,z,k),E&&(m[_]=A(E,y,(function(e){return E.call(P.target,P.eventName,e.invoke,P.options)}),z,k,!0)),m[i]=function(){var t=this||e,r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));var o=arguments[2],a=!!o&&("boolean"==typeof o||o.capture),i=arguments[1];if(!i)return D.apply(this,arguments);if(!s||s(D,i,t,arguments)){var c,u=X[r];u&&(c=u[a?h:p]);var l=c&&t[c];if(l)for(var f=0;f<l.length;f++){var d=l[f];if(I(d,i))return l.splice(f,1),d.isRemoved=!0,0===l.length&&(d.allRemoved=!0,t[c]=null,a||"string"!=typeof r||(t[v+"ON_PROPERTY"+r]=null)),d.zone.cancelTask(d),k?t:void 0}return D.apply(this,arguments)}},m[c]=function(){var t=this||e,r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));for(var o=[],a=ee(t,w?w(r):r),i=0;i<a.length;i++){var c=a[i];o.push(c.originalDelegate?c.originalDelegate:c.callback)}return o},m[l]=function(){var t=this||e,r=arguments[0];if(r){n&&n.transferEventName&&(r=n.transferEventName(r));var o=X[r];if(o){var a=t[o[p]],c=t[o[h]];if(a){var s=a.slice();for(v=0;v<s.length;v++)this[i].call(this,r,(u=s[v]).originalDelegate?u.originalDelegate:u.callback,u.options)}if(c)for(s=c.slice(),v=0;v<s.length;v++){var u;this[i].call(this,r,(u=s[v]).originalDelegate?u.originalDelegate:u.callback,u.options)}}}else{for(var f=Object.keys(t),v=0;v<f.length;v++){var d=J.exec(f[v]),_=d&&d[1];_&&"removeListener"!==_&&this[l].call(this,_)}this[l].call(this,"removeListener")}if(k)return this},x(m[o],O),x(m[i],D),j&&x(m[l],j),Z&&x(m[c],Z),!0}for(var w=[],P=0;P<n.length;P++)w[P]=E(n[P],r);return w}function ee(e,t){if(!t){var n=[];for(var r in e){var o=J.exec(r),a=o&&o[1];if(a&&(!t||a===t)){var i=e[r];if(i)for(var c=0;c<i.length;c++)n.push(i[c])}}return n}var s=X[t];s||($(t),s=X[t]);var u=e[s[p]],l=e[s[h]];return u?l?u.concat(l):u.slice():l?l.slice():[]}function te(e,t){var n=e.Event;n&&n.prototype&&t.patchMethod(n.prototype,"stopImmediatePropagation",(function(e){return function(t,n){t[K]=!0,e&&e.apply(t,n)}}))}function ne(e,t){t.patchMethod(e,"queueMicrotask",(function(e){return function(e,t){Zone.current.scheduleMicroTask("queueMicrotask",t[0])}}))}var re=g("zoneTask");function oe(e,t,n,r){var o=null,a=null;n+=r;var i={};function c(t){var n=t.data;n.args[0]=function(){return t.invoke.apply(this,arguments)};var r=o.apply(e,n.args);return B(r)?n.handleId=r:(n.handle=r,n.isRefreshable=G(r.refresh)),t}function s(t){var n=t.data,r=n.handle;return a.call(e,null!=r?r:n.handleId)}o=A(e,t+=r,(function(n){return function(o,a){var u;if(G(a[0])){var l={isRefreshable:!1,isPeriodic:"Interval"===r,delay:"Timeout"===r||"Interval"===r?a[1]||0:void 0,args:a},f=a[0];a[0]=function e(){try{return f.apply(this,arguments)}finally{var t=l.handle,n=l.handleId;l.isPeriodic||l.isRefreshable||(n?delete i[n]:t&&(t[re]=null))}};var h=_(t,a[0],l,c,s);if(!h)return h;var p=h.data,v=p.handleId,d=p.handle,g=p.isRefreshable,y=p.isPeriodic;if(v)i[v]=h;else if(d&&(d[re]=h,g&&!y)){var k=d.refresh;d.refresh=function(){var e=h.zone,t=h.state;return"notScheduled"===t?(h._state="scheduled",e._updateTaskCount(h,1)):"running"===t&&(h._state="scheduling"),k.call(this)}}return null!==(u=null!=d?d:v)&&void 0!==u?u:h}return n.apply(e,a)}})),a=A(e,n,(function(t){return function(n,r){var o,a=r[0];B(a)?(o=i[a],delete i[a]):(o=null==a?void 0:a[re])?a[re]=null:o=a,(null==o?void 0:o.type)?o.cancelFn&&o.zone.cancelTask(o):t.apply(e,r)}}))}function ae(e,t){if(!Zone[t.symbol("patchEventTarget")]){for(var n=t.getGlobalObjects(),r=n.eventNames,o=n.zoneSymbolEventNames,a=n.TRUE_STR,i=n.FALSE_STR,c=n.ZONE_SYMBOL_PREFIX,s=0;s<r.length;s++){var u=r[s],l=c+(u+i),f=c+(u+a);o[u]={},o[u][i]=l,o[u][a]=f}var h=e.EventTarget;if(h&&h.prototype)return t.patchEventTarget(e,t,[h&&h.prototype]),!0}}function ie(e,t,n){if(!n||0===n.length)return t;var r=n.filter((function(t){return t.target===e}));if(!r||0===r.length)return t;var o=r[0].ignoreProperties;return t.filter((function(e){return-1===o.indexOf(e)}))}function ce(e,t,n,r){e&&z(e,ie(e,t,n),r)}function se(e){return Object.getOwnPropertyNames(e).filter((function(e){return e.startsWith("on")&&e.length>2})).map((function(e){return e.substring(2)}))}function ue(e,t){if((!S||O)&&!Zone[e.symbol("patchEvents")]){var n=t.__Zone_ignore_on_properties,r=[];if(P){var o=window;r=r.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);var i=function e(){try{var e=k.navigator.userAgent;if(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/"))return!0}catch(e){}return!1}()?[{target:o,ignoreProperties:["error"]}]:[];ce(o,se(o),n?n.concat(i):n,a(o))}r=r.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(var c=0;c<r.length;c++){var s=t[r[c]];s&&s.prototype&&ce(s.prototype,se(s.prototype),n)}}}function le(e,t,n,r,o){var a=Zone.__symbol__(r);if(!t[a]){var i=t[a]=t[r];t[r]=function(a,c,s){return c&&c.prototype&&o.forEach((function(t){var o="".concat(n,".").concat(r,"::")+t,a=c.prototype;try{if(a.hasOwnProperty(t)){var i=e.ObjectGetOwnPropertyDescriptor(a,t);i&&i.value?(i.value=e.wrapWithCurrentZone(i.value,o),e._redefineProperty(c.prototype,t,i)):a[t]&&(a[t]=e.wrapWithCurrentZone(a[t],o))}else a[t]&&(a[t]=e.wrapWithCurrentZone(a[t],o))}catch(e){}})),i.call(t,a,c,s)},e.attachOriginToPatched(t[r],i)}}var fe="set",he="clear",pe=function ve(){var e,r=globalThis,o=!0===r[t("forceDuplicateZoneCheck")];if(r.Zone&&(o||"function"!=typeof r.Zone.__symbol__))throw new Error("Zone already loaded.");return null!==(e=r.Zone)&&void 0!==e||(r.Zone=n()),r.Zone}();!function de(e){(function t(e){e.__load_patch("ZoneAwarePromise",(function(e,t,n){var r=Object.getOwnPropertyDescriptor,o=Object.defineProperty,a=n.symbol,i=[],c=!1!==e[a("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],s=a("Promise"),u=a("then"),l="__creationTrace__";n.onUnhandledError=function(e){if(n.showUncaughtError()){var t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},n.microtaskDrainDone=function(){for(var e=function(){var e=i.shift();try{e.zone.runGuarded((function(){if(e.throwOriginal)throw e.rejection;throw e}))}catch(e){!function r(e){n.onUnhandledError(e);try{var r=t[f];"function"==typeof r&&r.call(this,e)}catch(e){}}(e)}};i.length;)e()};var f=a("unhandledPromiseRejectionHandler");function h(e){return e&&e.then}function p(e){return e}function v(e){return M.reject(e)}var d=a("state"),_=a("value"),g=a("finally"),y=a("parentPromiseValue"),k=a("parentPromiseState"),m="Promise.then",b=null,T=!0,E=!1,w=0;function S(e,t){return function(n){try{Z(e,t,n)}catch(t){Z(e,!1,t)}}}var P=function(){var e=!1;return function t(n){return function(){e||(e=!0,n.apply(null,arguments))}}},O="Promise resolved with itself",D=a("currentTaskTrace");function Z(e,r,a){var s=P();if(e===a)throw new TypeError(O);if(e[d]===b){var u=null;try{"object"!=typeof a&&"function"!=typeof a||(u=a&&a.then)}catch(t){return s((function(){Z(e,!1,t)}))(),e}if(r!==E&&a instanceof M&&a.hasOwnProperty(d)&&a.hasOwnProperty(_)&&a[d]!==b)C(a),Z(e,a[d],a[_]);else if(r!==E&&"function"==typeof u)try{u.call(a,s(S(e,r)),s(S(e,!1)))}catch(t){s((function(){Z(e,!1,t)}))()}else{e[d]=r;var f=e[_];if(e[_]=a,e[g]===g&&r===T&&(e[d]=e[k],e[_]=e[y]),r===E&&a instanceof Error){var h=t.currentTask&&t.currentTask.data&&t.currentTask.data[l];h&&o(a,D,{configurable:!0,enumerable:!1,writable:!0,value:h})}for(var p=0;p<f.length;)z(e,f[p++],f[p++],f[p++],f[p++]);if(0==f.length&&r==E){e[d]=w;var v=a;try{throw new Error("Uncaught (in promise): "+function e(t){return t&&t.toString===Object.prototype.toString?(t.constructor&&t.constructor.name||"")+": "+JSON.stringify(t):t?t.toString():Object.prototype.toString.call(t)}(a)+(a&&a.stack?"\n"+a.stack:""))}catch(e){v=e}c&&(v.throwOriginal=!0),v.rejection=a,v.promise=e,v.zone=t.current,v.task=t.currentTask,i.push(v),n.scheduleMicroTask()}}}return e}var j=a("rejectionHandledHandler");function C(e){if(e[d]===w){try{var n=t[j];n&&"function"==typeof n&&n.call(this,{rejection:e[_],promise:e})}catch(e){}e[d]=E;for(var r=0;r<i.length;r++)e===i[r].promise&&i.splice(r,1)}}function z(e,t,n,r,o){C(e);var a=e[d],i=a?"function"==typeof r?r:p:"function"==typeof o?o:v;t.scheduleMicroTask(m,(function(){try{var r=e[_],o=!!n&&g===n[g];o&&(n[y]=r,n[k]=a);var c=t.run(i,void 0,o&&i!==v&&i!==p?[]:[r]);Z(n,!0,c)}catch(e){Z(n,!1,e)}}),n)}var I=function(){},R=e.AggregateError,M=function(){function e(t){var n=this;if(!(n instanceof e))throw new Error("Must be an instanceof Promise.");n[d]=b,n[_]=[];try{var r=P();t&&t(r(S(n,T)),r(S(n,E)))}catch(e){Z(n,!1,e)}}return e.toString=function(){return"function ZoneAwarePromise() { [native code] }"},e.resolve=function(t){return t instanceof e?t:Z(new this(null),T,t)},e.reject=function(e){return Z(new this(null),E,e)},e.withResolvers=function(){var t={};return t.promise=new e((function(e,n){t.resolve=e,t.reject=n})),t},e.any=function(t){if(!t||"function"!=typeof t[Symbol.iterator])return Promise.reject(new R([],"All promises were rejected"));var n=[],r=0;try{for(var o=0,a=t;o<a.length;o++)r++,n.push(e.resolve(a[o]))}catch(e){return Promise.reject(new R([],"All promises were rejected"))}if(0===r)return Promise.reject(new R([],"All promises were rejected"));var i=!1,c=[];return new e((function(e,t){for(var o=0;o<n.length;o++)n[o].then((function(t){i||(i=!0,e(t))}),(function(e){c.push(e),0==--r&&(i=!0,t(new R(c,"All promises were rejected")))}))}))},e.race=function(e){var t,n,r=new this((function(e,r){t=e,n=r}));function o(e){t(e)}function a(e){n(e)}for(var i=0,c=e;i<c.length;i++){var s=c[i];h(s)||(s=this.resolve(s)),s.then(o,a)}return r},e.all=function(t){return e.allWithCallback(t)},e.allSettled=function(t){return(this&&this.prototype instanceof e?this:e).allWithCallback(t,{thenCallback:function(e){return{status:"fulfilled",value:e}},errorCallback:function(e){return{status:"rejected",reason:e}}})},e.allWithCallback=function(e,t){for(var n,r,o=new this((function(e,t){n=e,r=t})),a=2,i=0,c=[],s=function(e){h(e)||(e=u.resolve(e));var o=i;try{e.then((function(e){c[o]=t?t.thenCallback(e):e,0==--a&&n(c)}),(function(e){t?(c[o]=t.errorCallback(e),0==--a&&n(c)):r(e)}))}catch(e){r(e)}a++,i++},u=this,l=0,f=e;l<f.length;l++)s(f[l]);return 0==(a-=2)&&n(c),o},Object.defineProperty(e.prototype,Symbol.toStringTag,{get:function(){return"Promise"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Symbol.species,{get:function(){return e},enumerable:!1,configurable:!0}),e.prototype.then=function(n,r){var o,a=null===(o=this.constructor)||void 0===o?void 0:o[Symbol.species];a&&"function"==typeof a||(a=this.constructor||e);var i=new a(I),c=t.current;return this[d]==b?this[_].push(c,i,n,r):z(this,c,i,n,r),i},e.prototype.catch=function(e){return this.then(null,e)},e.prototype.finally=function(n){var r,o=null===(r=this.constructor)||void 0===r?void 0:r[Symbol.species];o&&"function"==typeof o||(o=e);var a=new o(I);a[g]=g;var i=t.current;return this[d]==b?this[_].push(i,a,n,n):z(this,i,a,n,n),a},e}();M.resolve=M.resolve,M.reject=M.reject,M.race=M.race,M.all=M.all;var L=e[s]=e.Promise;e.Promise=M;var N=a("thenPatched");function x(e){var t=e.prototype,n=r(t,"then");if(!n||!1!==n.writable&&n.configurable){var o=t.then;t[u]=o,e.prototype.then=function(e,t){var n=this;return new M((function(e,t){o.call(n,e,t)})).then(e,t)},e[N]=!0}}return n.patchThen=x,L&&(x(L),A(e,"fetch",(function(e){return function t(e){return function(t,n){var r=e.apply(t,n);if(r instanceof M)return r;var o=r.constructor;return o[N]||x(o),r}}(e)}))),Promise[t.__symbol__("uncaughtPromiseErrors")]=i,M}))})(e),function n(e){e.__load_patch("toString",(function(e){var t=Function.prototype.toString,n=g("OriginalDelegate"),r=g("Promise"),o=g("Error"),a=function a(){if("function"==typeof this){var i=this[n];if(i)return"function"==typeof i?t.call(i):Object.prototype.toString.call(i);if(this===Promise){var c=e[r];if(c)return t.call(c)}if(this===Error){var s=e[o];if(s)return t.call(s)}}return t.call(this)};a[n]=t,Function.prototype.toString=a;var i=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":i.call(this)}}))}(e),function a(e){e.__load_patch("util",(function(e,t,n){var a=se(e);n.patchOnProperties=z,n.patchMethod=A,n.bindArguments=T,n.patchMacroTask=L;var l=t.__symbol__("BLACK_LISTED_EVENTS"),f=t.__symbol__("UNPATCHED_EVENTS");e[f]&&(e[l]=e[f]),e[l]&&(t[l]=t[f]=e[l]),n.patchEventPrototype=te,n.patchEventTarget=Q,n.isIEOrEdge=q,n.ObjectDefineProperty=o,n.ObjectGetOwnPropertyDescriptor=r,n.ObjectCreate=i,n.ArraySlice=c,n.patchClass=R,n.wrapWithCurrentZone=d,n.filterProperties=ie,n.attachOriginToPatched=x,n._redefineProperty=Object.defineProperty,n.patchCallbacks=le,n.getGlobalObjects=function(){return{globalSources:Y,zoneSymbolEventNames:X,eventNames:a,isBrowser:P,isMix:O,isNode:S,TRUE_STR:h,FALSE_STR:p,ZONE_SYMBOL_PREFIX:v,ADD_EVENT_LISTENER_STR:s,REMOVE_EVENT_LISTENER_STR:u}}}))}(e)}(pe),function _e(e){e.__load_patch("legacy",(function(t){var n=t[e.__symbol__("legacyPatch")];n&&n()})),e.__load_patch("timers",(function(e){var t="set",n="clear";oe(e,t,n,"Timeout"),oe(e,t,n,"Interval"),oe(e,t,n,"Immediate")})),e.__load_patch("requestAnimationFrame",(function(e){oe(e,"request","cancel","AnimationFrame"),oe(e,"mozRequest","mozCancel","AnimationFrame"),oe(e,"webkitRequest","webkitCancel","AnimationFrame")})),e.__load_patch("blocking",(function(e,t){for(var n=["alert","prompt","confirm"],r=0;r<n.length;r++)A(e,n[r],(function(n,r,o){return function(r,a){return t.current.run(n,e,a,o)}}))})),e.__load_patch("EventTarget",(function(e,t,n){!function r(e,t){t.patchEventPrototype(e,t)}(e,n),ae(e,n);var o=e.XMLHttpRequestEventTarget;o&&o.prototype&&n.patchEventTarget(e,n,[o.prototype])})),e.__load_patch("MutationObserver",(function(e,t,n){R("MutationObserver"),R("WebKitMutationObserver")})),e.__load_patch("IntersectionObserver",(function(e,t,n){R("IntersectionObserver")})),e.__load_patch("FileReader",(function(e,t,n){R("FileReader")})),e.__load_patch("on_property",(function(e,t,n){ue(n,e)})),e.__load_patch("customElements",(function(e,t,n){!function r(e,t){var n=t.getGlobalObjects();(n.isBrowser||n.isMix)&&e.customElements&&"customElements"in e&&t.patchCallbacks(t,e.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"])}(e,n)})),e.__load_patch("XHR",(function(e,t){!function n(e){var n=e.XMLHttpRequest;if(n){var u=n.prototype,h=u[l],p=u[f];if(!h){var v=e.XMLHttpRequestEventTarget;if(v){var d=v.prototype;h=d[l],p=d[f]}}var y="readystatechange",k="scheduled",m=A(u,"open",(function(){return function(e,t){return e[o]=0==t[2],e[c]=t[1],m.apply(e,t)}})),b=g("fetchTaskAborting"),T=g("fetchTaskScheduling"),E=A(u,"send",(function(){return function(e,n){if(!0===t.current[T])return E.apply(e,n);if(e[o])return E.apply(e,n);var r={target:e,url:e[c],isPeriodic:!1,args:n,aborted:!1},a=_("XMLHttpRequest.send",P,r,S,O);e&&!0===e[s]&&!r.aborted&&a.state===k&&a.invoke()}})),w=A(u,"abort",(function(){return function(e,n){var o=function a(e){return e[r]}(e);if(o&&"string"==typeof o.type){if(null==o.cancelFn||o.data&&o.data.aborted)return;o.zone.cancelTask(o)}else if(!0===t.current[b])return w.apply(e,n)}}))}function S(e){var n=e.data,o=n.target;o[i]=!1,o[s]=!1;var c=o[a];h||(h=o[l],p=o[f]),c&&p.call(o,y,c);var u=o[a]=function(){if(o.readyState===o.DONE)if(!n.aborted&&o[i]&&e.state===k){var r=o[t.__symbol__("loadfalse")];if(0!==o.status&&r&&r.length>0){var a=e.invoke;e.invoke=function(){for(var r=o[t.__symbol__("loadfalse")],i=0;i<r.length;i++)r[i]===e&&r.splice(i,1);n.aborted||e.state!==k||a.call(e)},r.push(e)}else e.invoke()}else n.aborted||!1!==o[i]||(o[s]=!0)};return h.call(o,y,u),o[r]||(o[r]=e),E.apply(o,n.args),o[i]=!0,e}function P(){}function O(e){var t=e.data;return t.aborted=!0,w.apply(t.target,t.args)}}(e);var r=g("xhrTask"),o=g("xhrSync"),a=g("xhrListener"),i=g("xhrScheduled"),c=g("xhrURL"),s=g("xhrErrorBeforeScheduled")})),e.__load_patch("geolocation",(function(e){e.navigator&&e.navigator.geolocation&&function t(e,n){for(var o=e.constructor.name,a=function(t){var a=n[t],i=e[a];if(i){if(!E(r(e,a)))return"continue";e[a]=function(e){var t=function(){return e.apply(this,T(arguments,o+"."+a))};return x(t,e),t}(i)}},i=0;i<n.length;i++)a(i)}(e.navigator.geolocation,["getCurrentPosition","watchPosition"])})),e.__load_patch("PromiseRejectionEvent",(function(e,t){function n(t){return function(n){ee(e,t).forEach((function(r){var o=e.PromiseRejectionEvent;if(o){var a=new o(t,{promise:n.promise,reason:n.rejection});r.invoke(a)}}))}}e.PromiseRejectionEvent&&(t[g("unhandledPromiseRejectionHandler")]=n("unhandledrejection"),t[g("rejectionHandledHandler")]=n("rejectionhandled"))})),e.__load_patch("queueMicrotask",(function(e,t,n){ne(e,n)}))}(pe),function ge(e){(function t(e){e.__load_patch("node_util",(function(e,t,n){n.patchOnProperties=z,n.patchMethod=A,n.bindArguments=T,n.patchMacroTask=L,function r(e){M=e}(!0)}))})(e),function n(e){e.__load_patch("EventEmitter",(function(e,t,n){var r,o="addListener",a="removeListener",i=function(e,t){return e.callback===t||e.callback.listener===t},c=function(e){return"string"==typeof e?e:e?e.toString().replace("(","_").replace(")","_"):""};try{r=require("events")}catch(e){}r&&r.EventEmitter&&function s(t){var r=Q(e,n,[t],{useG:!1,add:o,rm:a,prepend:"prependListener",rmAll:"removeAllListeners",listeners:"listeners",chkDup:!1,rt:!0,diff:i,eventNameToString:c});r&&r[0]&&(t.on=t[o],t.off=t[a])}(r.EventEmitter.prototype)}))}(e),function r(e){e.__load_patch("fs",(function(e,t,n){var r,o;try{o=require("fs")}catch(e){}if(o){["access","appendFile","chmod","chown","close","exists","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","lutimes","link","lstat","mkdir","mkdtemp","open","opendir","read","readdir","readFile","readlink","realpath","rename","rmdir","stat","symlink","truncate","unlink","utimes","write","writeFile","writev"].filter((function(e){return!!o[e]&&"function"==typeof o[e]})).forEach((function(e){L(o,e,(function(t,n){return{name:"fs."+e,args:n,cbIdx:n.length>0?n.length-1:-1,target:t}}))}));var a=null===(r=o.realpath)||void 0===r?void 0:r[n.symbol("OriginalDelegate")];(null==a?void 0:a.native)&&(o.realpath.native=a.native,L(o.realpath,"native",(function(e,t){return{args:t,target:e,cbIdx:t.length>0?t.length-1:-1,name:"fs.realpath.native"}})))}}))}(e),e.__load_patch("node_timers",(function(e,t){var n=!1;try{var r=require("timers");if(e.setTimeout!==r.setTimeout&&!O){var o=r.setTimeout;r.setTimeout=function(){return n=!0,o.apply(this,arguments)};var a=e.setTimeout((function(){}),100);clearTimeout(a),r.setTimeout=o}oe(r,fe,he,"Timeout"),oe(r,fe,he,"Interval"),oe(r,fe,he,"Immediate")}catch(e){}O||(n?(e[t.__symbol__("setTimeout")]=e.setTimeout,e[t.__symbol__("setInterval")]=e.setInterval,e[t.__symbol__("setImmediate")]=e.setImmediate):(oe(e,fe,he,"Timeout"),oe(e,fe,he,"Interval"),oe(e,fe,he,"Immediate")))})),e.__load_patch("nextTick",(function(){N(process,"nextTick",(function(e,t){return{name:"process.nextTick",args:t,cbIdx:t.length>0&&"function"==typeof t[0]?0:-1,target:process}}))})),e.__load_patch("handleUnhandledPromiseRejection",(function(e,t,n){function r(e){return function(t){ee(process,e).forEach((function(n){"unhandledRejection"===e?n.invoke(t.rejection,t.promise):"rejectionHandled"===e&&n.invoke(t.promise)}))}}t[n.symbol("unhandledPromiseRejectionHandler")]=r("unhandledRejection"),t[n.symbol("rejectionHandledHandler")]=r("rejectionHandled")})),e.__load_patch("crypto",(function(){var e;try{e=require("crypto")}catch(e){}e&&["randomBytes","pbkdf2"].forEach((function(t){L(e,t,(function(n,r){return{name:"crypto."+t,args:r,cbIdx:r.length>0&&"function"==typeof r[r.length-1]?r.length-1:-1,target:e}}))}))})),e.__load_patch("console",(function(e,t){["dir","log","info","error","warn","assert","debug","timeEnd","trace"].forEach((function(e){var n=console[t.__symbol__(e)]=console[e];n&&(console[e]=function(){var e=c.call(arguments);return t.current===t.root?n.apply(this,e):t.root.run(n,this,e)})}))})),e.__load_patch("queueMicrotask",(function(e,t,n){ne(e,n)}))}(pe)}));