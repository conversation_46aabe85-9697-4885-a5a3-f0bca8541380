"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var a,n=1,r=arguments.length;n<r;n++)for(var e in a=arguments[n])Object.prototype.hasOwnProperty.call(a,e)&&(t[e]=a[e]);return t},__assign.apply(this,arguments)};
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(t){"function"==typeof define&&define.amd?define(t):t()}((function(){!function t(a){var n="\n",r={},e="__creationTrace__",c="STACKTRACE TRACKING",i="__SEP_TAG__",o=i+"@[native]",s=function s(){this.error=l(),this.timestamp=new Date};function _(){return new Error(c)}function f(){try{throw _()}catch(t){return t}}var u=_(),h=f(),l=u.stack?_:h.stack?f:_;function g(t){return t.stack?t.stack.split(n):[]}function k(t,a){for(var n=g(a),e=0;e<n.length;e++)r.hasOwnProperty(n[e])||t.push(n[e])}function T(t,a){var r=[a?a.trim():""];if(t)for(var e=(new Date).getTime(),c=0;c<t.length;c++){var s=t[c],_=s.timestamp,f="____________________Elapsed ".concat(e-_.getTime()," ms; At: ").concat(_);f=f.replace(/[^\w\d]/g,"_"),r.push(o.replace(i,f)),k(r,s.error),e=_.getTime()}return r.join(n)}function d(){return Error.stackTraceLimit>0}function p(t,a){a>0&&(t.push(g((new s).error)),p(t,a-1))}a.longStackTraceZoneSpec={name:"long-stack-trace",longStackTraceLimit:10,getLongStackTrace:function(t){if(t){var n=t[a.__symbol__("currentTaskTrace")];return n?T(n,t.stack):t.stack}},onScheduleTask:function(t,n,r,c){if(d()){var i=a.currentTask,o=i&&i.data&&i.data[e]||[];(o=[new s].concat(o)).length>this.longStackTraceLimit&&(o.length=this.longStackTraceLimit),c.data||(c.data={}),"eventTask"===c.type&&(c.data=__assign({},c.data)),c.data[e]=o}return t.scheduleTask(r,c)},onHandleError:function(t,n,r,c){if(d()){var i=a.currentTask||c.task;if(c instanceof Error&&i){var o=T(i.data&&i.data[e],c.stack);try{c.stack=c.longStack=o}catch(t){}}}return t.handleError(r,c)}},function v(){if(d()){var t=[];p(t,2);for(var a=t[0],n=t[1],e=0;e<a.length;e++)if(-1==(_=a[e]).indexOf(c)){var s=_.match(/^\s*at\s+/);if(s){o=s[0]+i+" (http://localhost)";break}}for(e=0;e<a.length;e++){var _;if((_=a[e])!==n[e])break;r[_]=!0}}}()}(Zone)}));