<div class="ion-intl-tel-input-code">
  <ionic-selectable
    #codeSelect
    [(ngModel)]="country"
    [canSearch]="true"
    closeButtonText="{{ modalCloseText }}"
    closeButtonSlot="{{ modalCloseButtonSlot }}"
    [disabled]="disabled"
    [hasVirtualScroll]="true"
    itemTextField="name"
    [items]="countries"
    itemValueField="dialCode"
    modalCssClass="ionic-tel-input-modal {{ modalCssClass }}"
    placeholder="Country"
    searchFailText="{{ modalSearchFailText }}"
    searchPlaceholder="Search country by name or code"
    [shouldBackdropClose]="modalShouldBackdropClose"
    [shouldFocusSearchbar]="true"
    (onChange)="onCodeChange($event)"
    (onClose)="onCodeClose()"
    (onOpen)="onCodeOpen()"
    (onSearch)="onCodeSearchCountries($event)"
    (onSelect)="onCodeSelect()"
  >
    <ng-template ionicSelectableTitleTemplate>
      {{ modalTitle }}
    </ng-template>
    <ng-template ionicSelectableValueTemplate let-country="value">
      <span class="fi fi-{{ country.flagClass }}"></span>
      <span *ngIf="separateDialCode"
        >{{ dialCodePrefix }}{{ country.dialCode }}</span
      >
    </ng-template>
    <ng-template ionicSelectableItemTemplate let-country="item">
      <span class="ion-margin-end">{{ country.name }}</span>
      <span *ngIf="separateDialCode"
        >{{ dialCodePrefix }}{{ country.dialCode }}</span
      >
    </ng-template>
    <ng-template ionicSelectableItemEndTemplate let-country="item">
      <span class="fi fi-{{ country.flagClass }}"></span>
    </ng-template>
  </ionic-selectable>
</div>

<div class="ion-intl-tel-input-number">
  <ion-input
    #numberInput
    [(ngModel)]="phoneNumber"
    autocomplete="off"
    [disabled]="disabled"
    [attr.maxLength]="maxLength"
    type="tel"
    legacy="true"
    (ionBlur)="onIonNumberBlur()"
    (ionChange)="onIonNumberChange($event)"
    (ionFocus)="onIonNumberFocus()"
    (ionInput)="onIonNumberInput($event)"
    (keydown)="onNumberKeyDown($event)"
    (ngModelChange)="onNumberChange()"
    [attr.aria-label]="label"
    placeholder="{{
      country
        | countryPlaceholder
          : inputPlaceholder
          : separateDialCode
          : fallbackPlaceholder
    }}"
  >
  </ion-input>
</div>
