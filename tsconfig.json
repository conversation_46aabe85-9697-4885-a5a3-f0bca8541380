{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "paths": {
      "ion-intl-tel-input": ["dist/ion-intl-tel-input"],
      // "mobile-components": ["dist/mobile-components"],
      // "mobile-components/*": ["dist/mobile-components/*"],
      "@projects/mobile-components": ["projects/mobile-components/src/public-api.ts"],
      "@projects/lp-client-api": ["projects/lp-client-api/src/public-api.ts"],
      "lp-client-api": ["dist/lp-client-api"],
      "lp-terminal": ["dist/lp-terminal"],
      "third-party-fix": ["dist/third-party-fix"],
      "@angular/*": ["./node_modules/@angular/*"],
      "@angular/cdk/*": ["./node_modules/@angular/cdk/*"],
      "lp-go": ["projects/lp-go/src/public-api.ts"],
      "keycloak-lp-ionic": ["projects/keycloak/keycloak.js"],
    },
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "moduleResolution": "node",
    "moduleDetection": "force",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2020",
    "lib": ["es2018", "dom"],
    "useDefineForClassFields": false,
    "types": ["google.maps", "node"],
    "allowJs": true,
    "skipLibCheck": true
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  },
  "include": [
    "projects/**/*.ts",
    "node_modules/@angular/**/*.ts",
    "projects/lp-client/src/environments/environment.web.ts.bak"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.spec.ts"
  ]
}
