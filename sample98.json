{"googleApiKey": "AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0", "apiId": "812275411", "appCode": "start", "appName": "Loyalty Client Mobile App", "appVersion": "0.0.1", "useAuth": true, "useION": true, "useISO": false, "defaultNotAuthURL": "", "autoLogout": true, "autoLogoutTimeout": 180, "autoLogoutWarning": 120, "defaultLat": -28.83688693522886, "defaultLng": 25.**************, "loadIdentity": false, "identityBaseUrl": "http://payroll.dv.lss.si/servlet/systemImage", "appBaseUrl": "http://alpine", "apiBaseUrl": "https://ffz1qa.loyaltyplus.aero/", "logEndpoint": "https://ffz1qa.loyaltyplus.aero/extsecure/tools/logservice", "configAPIUrl": "http://ffz1qa.loyaltyplus.aero/config/api/v1/", "memberPhone": {"dialCode": "+27", "nationalNumber": ""}, "memberCard": "", "telephone": {"selectFirstCountry": true, "preferredCountries": ["za"], "onlyCountries": ["za", "ls", "bw", "na", "sz", "mz"]}, "theme": {"layout": "sidebar", "backgroundImage": "", "colours": {"primary": "#f5821f", "primaryContrast": "#ffffff", "primaryShade": "#FFA500", "primaryTint": "#ff6600", "secondary": "#ffd400", "secondaryContrast": "#ffffff", "secondaryShade": "#4854e0", "secondaryTint": "#6370ff", "tertiary": "#ffd400", "tertiaryContrast": "#ffffff", "tertiaryShade": "#4854e0", "tertiaryTint": "#6370ff", "success": "#2dd36f", "successContrast": "#000000", "successShade": "#28ba62", "successTint": "#42d77d", "warning": "#ffc409", "warningContrast": "#000000", "warningShade": "#e0ac08", "warningTint": "#ffca22", "danger": "#eb445a", "dangerContrast": "#ffffff", "dangerShade": "#cf3c4f", "dangerTint": "#ed576b", "medium": "#92949c", "mediumContrast": "#000000", "mediumShade": "#808289", "mediumTint": "#9d9fa6", "base": "#0b69b4", "baseContrast": "#ffffff", "baseShade": "#16315d", "baseTint": "#4c8dff", "light": "#f4f5f8", "lightContrast": "#000000", "lightShade": "#d7d8da", "lightTint": "#f5f6f9", "step50": "#f8f8f8", "step100": "#f1f1f1", "step150": "#eaeaea", "step200": "#e3e3e3", "step250": "#dcdcdc", "step300": "#d5d5d5", "step350": "#cecece", "step400": "#c7c7c7", "step450": "#c0c0c0", "step500": "#b9b9b9", "step550": "#b2b2b2", "step600": "#ababab", "step650": "#a4a4a4", "step700": "#9d9d9d", "step750": "#969696", "step800": "#8f8f8f", "step850": "#888888", "step900": "#818181", "step950": "#7a7a7a"}}, "forms": {"login": {"options": {"email": true, "google": true}}, "contactus": {"categories": ["General", "<PERSON><PERSON><PERSON><PERSON>"]}}, "pages": {"landing": {"themes": ["theme-1", "theme-2", "theme-3", "theme-4"], "theme": "theme-1", "title": {"text": "Welcome"}, "subtitle": {"text": "Welcome"}, "balance": {"text": "Welcome"}, "action_card": {"profile": {"text": "Profile", "class_icon_outer": "action-icon text-baseShade", "class_icon": "w-4 h-4 -mt-4", "class_text": "text-xl -mt-5", "icon": "person-circle-outline"}, "card": {"text": "Card", "class_icon_outer": "action-icon text-baseShade", "class_icon": "w-4 h-4 -mt-4", "class_text": "text-xl -mt-5", "icon": "card-outline"}, "history": {"text": "Transactions", "class_icon_outer": "action-icon text-baseShade", "class_icon": "w-4 h-4 -mt-4", "class_text": "text-xl -mt-5", "icon": "cart-outline"}, "stores": {"text": "Stores", "class_icon_outer": "action-icon text-baseShade", "class_icon": "w-4 h-4 -mt-4", "class_text": "text-xl -mt-5", "icon": "location-outline"}, "contact": {"text": "Contact Us", "class_icon_outer": "action-icon text-baseShade", "class_icon": "w-4 h-4 -mt-4", "class_text": "text-xl -mt-5", "icon": "call-outline"}}, "icon": "assets/images/logo.png", "loggedinIcon": "assets/images/logo.png"}, "login": {"themes": ["theme-1", "theme-2", "theme-3", "theme-4"], "theme": "theme-1", "title": {"text": "Welcome"}, "subtitle": {"text": "to Make It With Loyalty"}, "icon": {"src": "assets/images/logo.png"}, "auth_buttons": {"login": {"text": "Sign in"}, "signup": {"text": "Sign up"}, "password": {"text": "Forgot Password"}}, "social_buttons": {"facebook": {"icon": "logo-facebook", "size": "large"}, "twitter": {"icon": "logo-twitter", "size": "large"}, "linkedin": {"icon": "logo-linkedin", "size": "large"}}, "loggedinIcon": "assets/images/logo.png"}, "contact": {"categoryCode": "CNCT", "icon": "assets/images/logo.png", "title": "Welcome", "subtitle": "Let us demo!"}, "profile": {"preferencesCode": "PART"}}, "contact": {"callCenter": "012 141 3596", "email": ""}, "socials": {"facebook": "https://www.facebook.com/micahardware", "twitter": "https://twitter.com/micahardware", "linkedin": "https://www.linkedin.com/company/mica-hardware?originalSubdomain=za"}, "navigation": {"sidebarTitle": "Mica", "sidebarIcon": "assets/images/logo.png", "type": "sidebar", "routes": [{"id": "<PERSON><PERSON>", "label": "<PERSON><PERSON>", "link": "login", "icon": "log-in-outline", "active": true, "sidebar": false, "admin": false, "sort": 0}, {"id": "Register", "label": "Register", "link": "register", "icon": "log-in-outline", "active": true, "sidebar": false, "admin": false, "sort": 0}, {"id": "Logout", "label": "Logout", "link": "logout", "icon": "log-out-outline", "active": true, "sidebar": false, "admin": false, "sort": 0}, {"id": "Home", "label": "Home", "link": "/", "icon": "home-outline", "active": true, "sidebar": true, "admin": false, "main": true, "sort": 0}, {"id": "Profile", "label": "Profile", "link": "/app/account", "icon": "person-outline", "active": true, "sidebar": true, "admin": false, "main": true, "sort": 0}, {"id": "Card", "label": "Card", "link": "/app/virtualcard", "icon": "card-outline", "active": true, "sidebar": true, "admin": false, "more": true, "sort": 0}, {"id": "Cha<PERSON>", "label": "Cha<PERSON>", "link": "/public/chat", "icon": "chatbubble-outline", "active": true, "sidebar": true, "admin": false, "main": true, "sort": 0}, {"id": "Transactions", "label": "Transactions", "link": "/app/transactions", "icon": "cart-outline", "active": true, "sidebar": true, "admin": false, "sort": 0}, {"id": "Stores", "label": "Stores", "link": "/app/stores", "icon": "location-outline", "active": true, "sidebar": true, "admin": false, "more": true, "sort": 0}, {"id": "Games", "label": "Games", "link": "/public/games/home", "icon": "game-controller-outline", "active": true, "sidebar": true, "admin": false, "more": true, "sort": 0}, {"id": "Contact", "label": "Contact Us", "link": "/secure/contactus", "icon": "call-outline", "active": true, "sidebar": true, "admin": false, "more": true, "sort": 0}]}, "useDemoProfile": false, "version": 98, "client": "FFZ1", "env": "QA"}